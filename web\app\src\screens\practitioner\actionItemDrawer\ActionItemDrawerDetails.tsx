import React, { useEffect } from "react";
import {
  Accordion,
  AttachmentHistory,
  ButtonSizeEnum,
  ButtonTypeEnum,
  CustomAvatar,
  CustomDatePicker,
  DocumentUpload,
  Icon,
  LocationItemChip,
  SearchDropdown,
  SelectedUser,
  Spinner,
  TextArea,
  Toggle,
  getInitials,
  getDateLabel,
  ActionItemStatusType,
  InlineMessage,
  getDueDateLabel,
  getDateTimeLabel,
  isPastDue,
  downloadFile,
} from "@bcp/uikit";
import { formatISO, isToday } from "date-fns";
import styles from "../shared/shared.module.css";
import {
  ActionItemDrawerProps,
  MAX_INSTRUCTIONS_CHARACTER_COUNT,
} from "../shared/spec";
import { useActionItemPriorityService } from "~/services/action-items/priority/userPriorityService";
import { useState } from "react";
import { ManageComments } from "../shared/comments/ManageComments";
import { useActionItemsService } from "~/services/action-items/useActionItemsService";
import { IUser } from "~/services/user/spec";
import { useUserService } from "~/services/user";
import {
  SignActionItemRequest,
  ActionItemDocument,
  ActionItemDocumentDetails,
  BGPSigningStatusOutcomeEnum,
  ActionItemAssignee,
} from "~/services/action-items/spec";
import { useTeamMembers } from "~/services/teamMembers";
import { useProjectService } from "~/services/project";
import { useClientService } from "~/services/client";
import { useLocation } from "react-router-dom";
import classNames from "classnames";
import i18n from "~/shell/i18n";
import { useToastService } from "~/services/toast";
import { acceptedFileTypes } from "~/screens/practitioner/shared/spec";
import { useRoleService } from "~/services/role";
import { parseJSON } from "date-fns";
import { useTranslation } from "react-i18next";
import { useSettingsService } from "~/services/settings";
import {
  generateAttachedDocumentDownloadUrl,
  isActionItemDocumentRequest,
  isActionItemSignatureRequest,
  isCurrentUserActionItemAssignee,
  sortByName,
  isUserTurnPending,
} from "~/utils/actionItems/actionItemUtils";
import { InformationBanner } from "../createActionItem/informationBanner/InformationBanner";
import { Assignees } from "./assignees/Assignees";

type DocumentWithDetails = ActionItemDocument & {
  details: ActionItemDocumentDetails;
};

const documentHasDetails = (
  document: ActionItemDocument
): document is DocumentWithDetails => {
  return document.details !== null;
};

export const ActionItemDrawerDetails: React.FC<ActionItemDrawerProps> = ({
  actionItemDetails,
  setSelectedAssignees,
  selectedAssignees,
  description,
  setDescription,
  dueDate,
  setDueDate,
  files,
  setFiles,
  isPriority,
  setIsPriority,
  documents,
  fileUploadDestination,
  shouldReset,
  onCommentTextChange,
}) => {
  const { teamMembers } = useTeamMembers();
  const { updatePriority } = useActionItemPriorityService();
  const { fetchActionItems, sign } = useActionItemsService();
  const { profile: currentUser } = useUserService();
  const { activeProject, spoSite } = useProjectService();
  const { activeClient } = useClientService();
  const { showToast, hideAllToasts } = useToastService();
  const { isPractitioner } = useRoleService();
  const { t: tActionItemPage } = useTranslation("actionItems");
  const { t: tErrorMessages } = useTranslation("error");
  const { t: tDocuments } = useTranslation("documents");
  const { settings } = useSettingsService();

  const [suggestedUsers, setSuggestedUsers] = useState<IUser[]>([]);
  const [isSigningLoading, setIsSigningLoading] = useState<boolean>(false);

  const isDocumentRequest = isActionItemDocumentRequest(actionItemDetails.type);
  const isSignatureRequest = isActionItemSignatureRequest(
    actionItemDetails.type
  );

  const isActionItemTodo =
    actionItemDetails.status === ActionItemStatusType.TODO;
  const isActionItemInReview =
    actionItemDetails.status === ActionItemStatusType.INREVIEW;

  const isCurrentUserAssignee = isCurrentUserActionItemAssignee(
    actionItemDetails.assignees,
    currentUser?.data?.bgpId
  );
  const showDocumentUpload =
    isDocumentRequest && isActionItemTodo && isCurrentUserAssignee;

  const isPractitionerEditMode =
    isPractitioner &&
    isDocumentRequest &&
    isActionItemTodo &&
    !isCurrentUserAssignee;

  const location = useLocation();

  useEffect(() => {
    if (
      teamMembers &&
      !teamMembers.isBusy &&
      teamMembers.data &&
      currentUser &&
      !currentUser.isBusy &&
      currentUser.data
    ) {
      // Filter the current user out of suggested users for assignees of the Action item
      setSuggestedUsers(
        teamMembers.data
          .filter(teamMember => teamMember.id !== currentUser.data?.id)
          .sort(sortByName)
      );
    }
  }, [teamMembers, currentUser]);

  const handleSignDocumentClick = async (actionItemId: string) => {
    setIsSigningLoading(true);
    if (!activeProject || !activeClient || !activeClient.bgpId) return;

    try {
      const payload: SignActionItemRequest = {
        taskId: actionItemId,
        projectId: activeProject.bgpId,
        clientId: activeClient.bgpId,
        customReturnUrl: location.pathname + `?actionItemId=${actionItemId}`,
      };
      const { docusignUrl } = await sign(payload);
      if (docusignUrl) window.open(docusignUrl, "_self");
    } catch {
      setIsSigningLoading(false);
      showToast({
        type: "error",
        message: tActionItemPage("signature-request-error-message"),
        persist: false,
      });
    }
  };

  const handleDownloadDocumentClick = (
    documentUrl: string,
    documentName: string
  ) => {
    if (!settings?.data?.spoUrl) return;
    downloadFile(documentUrl, documentName);
  };

  const doesUserHaveSigningStatus = (
    assignees: ActionItemAssignee[],
    userId: string,
    status: BGPSigningStatusOutcomeEnum
  ) =>
    assignees.some(
      assignee => assignee.outcome === status && assignee.user.bgpId === userId
    );

  const downloadDocumentButtonConfig = (
    documentUrl: string,
    documentName: string
  ) => ({
    label: tActionItemPage("download"),
    type: ButtonTypeEnum.secondary,
    onClick: () => handleDownloadDocumentClick(documentUrl, documentName),
    size: ButtonSizeEnum.small,
    rightIconName: "arrow-download",
    isLoading: isSigningLoading,
  });

  const signDocumentButtonConfig = (actionItemId: string) => ({
    label: tActionItemPage("sign"),
    type: ButtonTypeEnum.primary,
    onClick: () => handleSignDocumentClick(actionItemId),
    size: ButtonSizeEnum.small,
    rightIconName: "signature-icon",
    isLoading: isSigningLoading,
    disabled: isCurrentUserTurnPending,
  });

  /* TODO: simplify for readability and clarity */
  /* Signing Request Button Display Logic:
    1. Current User is an Assignee (Client OR Practitioner)
      a. Not Signed
          i. Show "Sign" CTA
      b. Signed
          i. If Practitioner show "Open" CTA
          ii. If Client User no CTA - defaults to Download btn
     2. Current User is not an Assignee
      a. If Practitioner
          i. Show "Open" CTA
      b. If Client
          i. Defaults to Download btn */

  const getSigningDocumentButtonConfig = (
    document: ActionItemDocumentDetails,
    documentUrl: string
  ) => {
    if (!currentUser?.data) return null;

    if (isCurrentUserAssignee) {
      if (
        doesUserHaveSigningStatus(
          actionItemDetails.assignees,
          currentUser.data.bgpId,
          BGPSigningStatusOutcomeEnum.Unsigned
        ) &&
        actionItemDetails.status === ActionItemStatusType.TODO
      ) {
        return signDocumentButtonConfig(actionItemDetails.id);
      } else if (isPractitioner) {
        return downloadDocumentButtonConfig(documentUrl, document.name);
      }
    } else if (isPractitioner) {
      return downloadDocumentButtonConfig(documentUrl, document.name);
    }
    return null;
  };

  const generateAttachmentHistoryFiles = () => {
    const _spoUrl = settings?.data?.spoUrl;
    if (!_spoUrl) return;
    if (!spoSite) return;
    if (documents.length > 0) {
      const files = documents.filter(documentHasDetails).map(document => {
        const documentUrl = generateAttachedDocumentDownloadUrl(
          spoSite,
          document.details.serverRelativeUrl
        );

        return {
          name: document.details.name,
          url: documentUrl,
          author: document.details.author,
          timeCreated: parseJSON(document.details.timeCreated),
          btnConfig: isSignatureRequest
            ? getSigningDocumentButtonConfig(document.details, documentUrl)
            : null,
        };
      });
      return files;
    }
    return null;
  };

  const handleMarkPriority = async (currentPriority: boolean) => {
    if (!activeProject) return;
    const previousPriority = currentPriority;
    setIsPriority(!previousPriority);
    const result = await updatePriority(
      !previousPriority,
      actionItemDetails.id,
      activeProject.bgpId
    );

    if (result.data) {
      showToast({
        type: "success",
        message: tActionItemPage("priority-change-success-message"),
        persist: false,
      });
    } else if (result.error) {
      setIsPriority(previousPriority);
      showToast({
        type: "error",
        message: tErrorMessages("fatal.message"),
        persist: false,
      });
    }
  };

  const getDeclinedAssignee = (
    assignees: ActionItemAssignee[]
  ): ActionItemAssignee | undefined =>
    assignees.find(
      (assignee: ActionItemAssignee) =>
        assignee.outcome === BGPSigningStatusOutcomeEnum.Declined
    );

  //Temporary solution for WYSIWYG value
  const instructions = `<div>${actionItemDetails?.description}</div>`;

  const handleUserAdd = (selectedUser: SelectedUser) => {
    const user = teamMembers.data?.find(
      user => user.bgpId === selectedUser.uniqueUserId
    );

    // TODO: Update SearchDropdown component to avoid this `find`
    // This case should not be possible; types need improvement
    if (!user) {
      console.error("Failed to add user: did not find in Team Members");
      return;
    }

    const newAssignee = { user };
    const updatedAssignees = [...selectedAssignees, newAssignee];
    setSelectedAssignees(updatedAssignees);
  };

  const handleUserRemove = (removedUser: SelectedUser) => {
    // This case should not be possible; types need improvement
    if (!removedUser.uniqueUserId) {
      console.error("Failed to remove user: missing id");
      return;
    }

    const updatedAssignees = selectedAssignees.filter(
      assignee => assignee.user.bgpId !== removedUser.uniqueUserId
    );

    setSelectedAssignees(updatedAssignees);
  };

  if (
    !settings ||
    !settings.data ||
    !currentUser ||
    !currentUser.data ||
    !activeClient ||
    !activeClient.bgpId ||
    !activeProject ||
    !activeProject.bgpId
  ) {
    return null;
  }

  const currentUserHasSigned =
    isCurrentUserAssignee &&
    doesUserHaveSigningStatus(
      actionItemDetails.assignees,
      currentUser.data.bgpId,
      BGPSigningStatusOutcomeEnum.Signed
    );

  const isDocumentPendingSignatures =
    currentUserHasSigned &&
    actionItemDetails.status !== ActionItemStatusType.COMPLETE &&
    actionItemDetails.status !== ActionItemStatusType.DECLINED;

  const declinedUser =
    isSignatureRequest &&
    actionItemDetails.status == ActionItemStatusType.DECLINED &&
    getDeclinedAssignee(actionItemDetails.assignees);

  const isCurrentUserTurnPending = isUserTurnPending(
    actionItemDetails,
    currentUser.data
  );

  return (
    <div className={styles.actionItemsDetails}>
      <div className={styles.properties}>
        <div className={styles.propertyRows}>
          {declinedUser && declinedUser.statusDate && (
            <InlineMessage
              data-testid="user-rejection-reason"
              type="warning"
              title={
                isToday(declinedUser.statusDate)
                  ? tActionItemPage("decline-signing-message-today", {
                      name: declinedUser.user.displayName,
                      datetime: getDateTimeLabel(
                        formatISO(declinedUser.statusDate),
                        i18n.language,
                        true
                      ),
                    })
                  : tActionItemPage("decline-signing-message", {
                      name: declinedUser.user.displayName,
                      datetime: getDateTimeLabel(
                        formatISO(declinedUser.statusDate),
                        i18n.language,
                        true
                      ),
                    })
              }
              message={
                declinedUser.rejectionReason
                  ? tActionItemPage("decline-signing-reason", {
                      reason: declinedUser.rejectionReason,
                    })
                  : ""
              }
              withIcon={false}
            />
          )}
          {actionItemDetails.signingOrderEnabled &&
            isCurrentUserTurnPending &&
            !declinedUser && (
              <InformationBanner
                text={tActionItemPage("signing-order-pending")}
                message={tActionItemPage("signing-order-pending-message")}
              />
            )}
          {actionItemDetails.signingOrderEnabled &&
            !isCurrentUserAssignee &&
            !declinedUser && (
              <InformationBanner
                text={tActionItemPage("signing-order-enabled")}
                message={tActionItemPage("signing-order-enabled-message")}
              />
            )}
          <div className={styles.propertyRow}>
            <div className={styles.caption}>
              <Icon iconName="calendar-ltr" />
              {tActionItemPage("due-date")}
            </div>
            <div className={styles.dateInputWrapper}>
              {isPractitionerEditMode ? (
                !shouldReset && (
                  <CustomDatePicker
                    placeholder="MM / DD / YYYY"
                    selectionMode="single"
                    onChange={setDueDate}
                    enableRange={false}
                    closeOnSelect
                    calendarId="action-items-details-calendar-id"
                    value={dueDate}
                    shouldReset={shouldReset}
                    placement="bottom-start"
                  />
                )
              ) : actionItemDetails.dueDate ? (
                <span
                  className={classNames({
                    [styles.dueDatePastDue]: isPastDue(
                      formatISO(actionItemDetails.dueDate)
                    ),
                  })}
                >
                  {getDueDateLabel(
                    formatISO(actionItemDetails.dueDate),
                    i18n.language
                  )}
                </span>
              ) : (
                "N/A"
              )}
            </div>
          </div>
          <div className={styles.propertyRow}>
            <div className={styles.caption}>
              <Icon iconName="people-icon" />
              {tActionItemPage("assignees")}
            </div>
            {isPractitionerEditMode ? (
              <div className={styles.searchDropdownWrapper}>
                {!teamMembers.isBusy ? (
                  <SearchDropdown
                    ariaLabel={tActionItemPage("assignees")}
                    id="action-item-assignees-dropdown"
                    suggestedUsers={
                      suggestedUsers.length > 0
                        ? suggestedUsers
                            .filter(
                              member =>
                                !selectedAssignees.find(selected => {
                                  return selected.user.email === member.email;
                                })
                            )
                            .sort(sortByName)
                            .map((member, i) => {
                              return {
                                name: `${member.firstName} ${member.lastName}`,
                                email: member.email as string,
                                avatarColor: member.avatarColor,
                                uniqueUserId: member.bgpId,
                                id: i,
                              };
                            })
                        : []
                    }
                    selectedUsers={selectedAssignees.map(assignee => ({
                      displayText: assignee.user.displayName,
                      name: assignee.user.displayName,
                      email: assignee.user.email,
                      id: assignee.user.bgpId,
                      uniqueUserId: assignee.user.bgpId,
                    }))}
                    onUserAdd={handleUserAdd}
                    onUserRemove={handleUserRemove}
                    showEmail={false}
                    allowUnverifiedUsers={false}
                  />
                ) : (
                  <div className={styles.searchDropdownLoader}>
                    <Spinner isLoading={true} size={"large"} />
                  </div>
                )}
              </div>
            ) : (
              <Assignees
                assignees={actionItemDetails.assignees}
                view={isSignatureRequest ? "detailed" : "simple"}
              />
            )}
          </div>
          <div className={styles.propertyRow}>
            <div className={styles.caption}>
              <Icon iconName="folder-icon" altText="folder icon" />
              {tActionItemPage("file-destination")}
            </div>
            <LocationItemChip
              text={
                fileUploadDestination?.displayName ?? tDocuments("Documents")
              } // default fallback is root
              iconName="folder-icon"
            />
          </div>
          {isPractitioner && (
            <div className={styles.propertyRow}>
              <div className={styles.caption}>
                <Icon iconName="chevron-double" altText="priority" />
                {tActionItemPage("priority")}
              </div>
              <div className={styles.toggleWrapper}>
                <Toggle
                  ariaLabel={tActionItemPage("priority")}
                  isDefaultChecked={isPriority}
                  onCheckedChange={async () => {
                    // If editing, priority toggle changes only applied on Action Item edit "Save"
                    if (isPractitionerEditMode) {
                      setIsPriority(currentPriority => !currentPriority);
                      return;
                    }
                    //If not editing, priority switch toggles applied instantly
                    await handleMarkPriority(isPriority);
                    fetchActionItems(actionItemDetails.projectId);
                  }}
                  isDisabled={isCurrentUserAssignee} // Practitioner that is an assignee cannot toggle priority
                />
              </div>
            </div>
          )}
        </div>
        <Accordion
          id="accordion"
          title={tActionItemPage("additional-properties")}
        >
          <div
            className={classNames(
              styles.additionalProperties,
              styles.additionalPropertiesTwoCol
            )}
            key="additional-properties"
          >
            <div className={styles.property}>
              {tActionItemPage("created-by")}
              <span
                className={classNames(
                  styles.propertyValue,
                  styles["propertyValue--bold"]
                )}
              >
                <CustomAvatar
                  avatarSize="x-small"
                  fontSize="x-small"
                  type="monogram"
                  initials={getInitials(actionItemDetails.owner.name)}
                />
                {actionItemDetails.owner.name}
              </span>
            </div>

            <div className={styles.property}>
              {tActionItemPage("date-created")}
              <span className={styles.propertyValue}>
                {getDateLabel(
                  formatISO(actionItemDetails.createdOn),
                  i18n.language
                )}
              </span>
            </div>
          </div>
        </Accordion>
      </div>
      <div className={styles.files}>
        {!isPractitionerEditMode ? (
          description && (
            <div className={styles.instructionsWrapper}>
              <div>
                <label className={styles.instructionsLabel}>
                  {tActionItemPage("instructions")}
                </label>
                <div
                  className={styles.instructionsDescription}
                  dangerouslySetInnerHTML={{ __html: instructions }}
                />
              </div>
            </div>
          )
        ) : (
          <div className={styles.instructionsWrapper}>
            <TextArea
              label={tActionItemPage("instructions")}
              hideLabel={false}
              value={description}
              onChange={e => setDescription(e)}
              maxLength={MAX_INSTRUCTIONS_CHARACTER_COUNT}
              displayMaxLength
            />
          </div>
        )}
        <AttachmentHistory
          attachmentHistories={generateAttachmentHistoryFiles() || []}
          type={actionItemDetails.type}
          isPendingStatus={isCurrentUserAssignee && isDocumentPendingSignatures}
        />
        {showDocumentUpload && (
          <DocumentUpload
            id="action-item-details-documents"
            showDropZone
            username={currentUser?.data?.displayName}
            title={tActionItemPage("document-upload")}
            subtitle={tActionItemPage("upload-request-docs")}
            subtext={tActionItemPage("accepted-files")}
            showToast={showToast}
            hideAllToasts={hideAllToasts}
            acceptedFileTypes={acceptedFileTypes}
            onFileAccept={setFiles}
            onFileDelete={setFiles}
            existingFiles={files}
            required={true}
          />
        )}
      </div>
      <ManageComments
        actionItemId={actionItemDetails.id}
        isCommentingDisabled={isActionItemInReview}
        onCommentTextChange={onCommentTextChange}
      />
    </div>
  );
};

export default ActionItemDrawerDetails;
