import { Button, ButtonSizeEnum, ButtonTypeEnum, Icon, ButtonDropdown, ButtonDropdonItem } from "@bcp/uikit";
import styles from "./bulkContainer.module.css";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";

interface BulkBannerProps extends defaultProps {
  selectedRows: string[];
  setSelectedRows: React.Dispatch<React.SetStateAction<string[]>>;
  shouldHideMoveButton: boolean;
  onClickDownload: () => void;
  onClickMove: () => void;
  onClickMoveToBin: () => void;
  onClickExportToApt?: () => void;
  isLinkedToApt?: boolean;
}

export const BulkBanner: React.FC<BulkBannerProps> = ({
  selectedRows,
  setSelectedRows,
  dataTestId = "uikit-bulkBanner",
  ariaLabel = `${selectedRows.length} selected for bulk download`,
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  role = "banner",
  shouldHideMoveButton,
  onClickDownload,
  onClickMove,
  onClickMoveToBin,
  onClickExportToApt,
  isLinkedToApt = false,
}) => {
  const { t } = useTranslation("documents");

  // Export dropdown items
  const exportDropdownItems: ButtonDropdonItem[] = [
    {
      id: "export-apt",
      onClick: () => {
        onClickExportToApt?.();
      },
      iconName: "arrow-upload",
      label: "APT",
      value: "apt",
    },
  ];

  return (
    <div
      className={styles.container}
      data-testid={dataTestId}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
      role={role}
    >
      <div className={styles.leftSection}>
        <button
          aria-label={t("unselect-all", { count: selectedRows.length })}
          className={styles.closeIconContainer}
          onClick={() => setSelectedRows([])}
        >
          <Icon iconName="close-icon" altText="Close" size={16} />
        </button>
        <span className={styles.selectedText}>
          {t("items-selected", { count: selectedRows.length })}
        </span>
      </div>
      <div className={styles.buttonsContainer}>
        {!isLinkedToApt && (
          <div className={styles.buttonWrapper}>
            <ButtonDropdown
              id="export-dropdown"
              triggerText={t("export")}
              triggerIcon="chevron-down"
              items={exportDropdownItems}
              iconColor="charcoal"
              dataTestId="exportToAptButton"
            />
          </div>
        )}
        <div className={styles.buttonWrapper}>
          <Button
            id="bulk-download-button"
            rightIconName="arrow-download"
            label={t("download-selected")}
            withRightIcon
            onClick={() => {
              onClickDownload();
            }}
            type={ButtonTypeEnum.tertiary}
            size={ButtonSizeEnum.icon}
            dataTestId={"downloadFilesButton"}
            ariaLabel={`Download ${selectedRows.length} files`}
          />
        </div>
        {!shouldHideMoveButton && (
          <div className={styles.buttonWrapper}>
            <Button
              id="bulk-move-button"
              rightIconName="folder-arrow-right"
              label={t("move-cta")}
              withRightIcon
              onClick={async () => {
                await onClickMove();
              }}
              type={ButtonTypeEnum.tertiary}
              size={ButtonSizeEnum.icon}
              dataTestId={"moveFilesButton"}
              ariaLabel={`Move ${selectedRows.length} files`}
            />
          </div>
        )}
        {!shouldHideMoveButton && (
          <div className={styles.buttonWrapper}>
            <Button
              id="bulk-recycle-button"
              rightIconName="move-document-to-recycle-bin-icon"
              label={t("move-to-recycle-bin")}
              withRightIcon
              onClick={async () => {
                await onClickMoveToBin();
              }}
              type={ButtonTypeEnum.tertiary}
              size={ButtonSizeEnum.icon}
              dataTestId={"deleteItemsButton"}
              ariaLabel={`Deleting ${selectedRows.length} items`}
              isWarning
            />
          </div>
        )}
      </div>
    </div>
  );
};
