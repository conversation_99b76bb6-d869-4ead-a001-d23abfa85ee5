import styles from "./assigneesSimple.module.css";
import { ActionItemAssignee } from "~/services/action-items/spec";
import { CustomAvatar, getInitials } from "@bcp/uikit";

interface Props {
  assignees: ActionItemAssignee[];
}

export const AssigneesSimple: React.FC<Props> = ({ assignees }) => {
  return (
    <div className={styles.container}>
      {assignees.map(assignee => {
        const initials = getInitials(assignee.user.displayName);
        return (
          <span className={styles.assignee} key={assignee.user.bgpId}>
            <CustomAvatar
              avatarSize="x-small"
              fontSize="x-small"
              type="monogram"
              initials={initials}
            />
            {assignee.user.displayName}
          </span>
        );
      })}
    </div>
  );
};
