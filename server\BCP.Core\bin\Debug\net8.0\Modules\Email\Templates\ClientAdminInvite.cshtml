@model BCP.Core.Email.Templates.ClientAdminInvite

@{
  Layout = "Common/Layout.cshtml";
  var T = Model.Translation;
}

<table role="presentation" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <p style="margin:0 0 16px 0; color:#333; font-size:16px; line-height:24px;">
        @Model.Greeting
      </p>
      <p style="margin:0 0 16px 0; color:#333; font-size:16px; line-height:24px;">
        @T.ClientAdminInvite_Body
      </p>
    </td>
  </tr>
  <tr>
    <td style="padding:16px 0;">
      <h1 style="margin:0; color:#333; font-size:22px; line-height:32px; font-weight:400;">
        @Model.Client?.Name
      </h1>
    </td>
  </tr>
  <tr>
    <td style="padding-bottom:40px;">
      <table role="presentation" cellpadding="0" cellspacing="0">
        <tr>
          <td style="background:#e81a3b; padding:8px 16px; border-radius:2px;">
            <a
              href="@Model.PortalUrl/client/@Model.Client?.Id/project/@Model.Project?.Id"
              target="_blank"
              style="color:#fff; text-decoration:none; font-size:16px; line-height:24px; font-weight:300;"
            >
              @T.ClientAdminInvite_Cta &rarr;
            </a>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="padding-bottom:16px;">
      <h2 style="margin:0; color:#333; font-size:16px; line-height:24px; font-weight:600;">
        @T.ClientAdminInvite_StepTitle
      </h2>
    </td>
  </tr>
  <tr>
    <td style="padding-bottom:40px; border-bottom:1px solid #cccfd2;">
      <table role="presentation" cellpadding="0" cellspacing="0">
        @foreach (var (title, description, icon) in T.ClientAdminInvite_Steps) {
          <tr>
            <td width="48" style="width:48px; padding:16px 0;">
              <img
                src="@($"{Model.AssetUrl}/icon-{icon}.png")"
                alt="@icon icon"
                width="48"
                style="width:48px; height:auto; border:0;"
              />
            </td>
            <td style="padding:16px;">
              <h3 style="margin:0; color:#333; font-size:16px; line-height:24px; font-weight:600;">
                @title
              </h3>
              <p style="margin:0; color:#333; font-size:14px; line-height:20px;">
                @description
              </p>
            </td>
          </tr>
        }
      </table>
    </td>
  </tr>
  <tr>
    <td style="padding-top:40px;">
      <h2 style="margin:0; color:#333; font-size:16px; line-height:24px; font-weight:600;">
        @T.ClientAdminInvite_HelpTitle
      </h2>
      <p style="margin:16px 0; color:#333; font-size:14px; line-height:20px;">
        @T.ClientAdminInvite_HelpBody
      </p>
    </td>
  </tr>
  <tr>
    <td>
      <table role="presentation" cellpadding="0" cellspacing="0">
        <tr>
          <td style="background:#fff; padding:8px 16px; border:1px solid #333; border-radius:2px;">
            <a
              href="https://bdocanada.sharepoint.com/sites/ClientDigitalExperienceCoE/SitePages/Home.aspx"
              target="_blank"
              style="color:#333; text-decoration:none; font-size:16px; line-height:24px; font-weight:300;"
            >
              @T.ClientAdminInvite_ResourceName &rarr;
            </a>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>

@section Footer {
  <p style="font-size:12px; line-height:18px; color:#666; margin:0 0 16px 0;">
    @T.Notification_Footer_Note <a href="@Model.PortalUrl/settings/notifications">@T.Notification_Footer_Cta</a>.
  </p>
}
