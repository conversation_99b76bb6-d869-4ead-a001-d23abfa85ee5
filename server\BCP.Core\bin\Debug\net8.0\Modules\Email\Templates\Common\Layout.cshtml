@using BCP.Core.Email
@model BCP.Core.Email.ITemplateModelBase

@{
  var T = Model.Translation;
}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>@Model.Subject</title>
  <style>
    body, table, td { font-family: Arial, sans-serif !important; }
  </style>
</head>

<body style="margin:0; padding:0; background-color:#f4f4f4;">

  <table
    role="presentation"
    width="100%"
    cellpadding="0"
    cellspacing="0"
    border="0"
    style="border-collapse:collapse; background-color:#f4f4f4;"
  >
    <tr>
      <td align="center" style="padding:40px 0; font-family:Arial, sans-serif;">

        <!--[if lte mso 14]>
          <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:768px;">
            <v:fill type="tile" color="#ffffff" />
            <v:textbox inset="0,0,0,0">
        <![endif]-->

        <table
          role="presentation"
          width="768"
          cellpadding="0"
          cellspacing="0"
          border="0"
          style="width:768px; border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt; background-color:#fff;"
        >

          <!-- Header -->
          <tr>
            <td style="padding:16px 40px; border-bottom:1px solid #dcdcdc;">
              <table
                role="presentation"
                cellpadding="0"
                cellspacing="0"
                border="0"
                style="border-collapse:collapse;"
              >
                <tr>
                  <td style="width:48px; height:40px; padding-right:16px; border-right:1px solid #dcdcdc;">
                    <img
                      src="@Model.AssetUrl/bdo-logo.png"
                      alt="BDO logo"
                      width="48"
                      style="width:48px; border:0;"
                    />
                  </td>
                  <td style="padding-left:16px; font-family:Arial, sans-serif; font-size:12px; color:#333;">
                    @if (Model.Client != null)
                    {
                      @Model.Client?.Name
                    }
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td style="@(Model.Layout == EmailLayout.Narrow ? "padding:64px 120px;" : "padding:40px;")">
              @RenderBody()
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="@(Model.Layout == EmailLayout.Narrow ? "padding: 0 120px 64px 120px;" : "padding: 0 40px 40px 40px;")">
              @RenderSection("Footer", required: false)

              <p style="margin:16px 0 0 0; font-size:12px; line-height:18px; color:#666;">&copy; 2025 BDO.</p>
            </td>
          </tr>
        </table>

        <!--[if lte mso 14]>
            </v:textbox>
          </v:rect>
        <![endif]-->

      </td>
    </tr>
  </table>

</body>

</html>
