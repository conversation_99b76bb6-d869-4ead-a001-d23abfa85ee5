{"openapi": "3.0.1", "info": {"title": "BCP API", "version": "v1"}, "paths": {"/api/actionitem/priority": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/unmarkpriority": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/actionitems": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "memberFirmId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/actionitem": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "memberFirmId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "actionItemId", "in": "query", "required": true, "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/addcomment": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOAddCommentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOAddCommentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOAddCommentRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/updatecomment": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUpdateCommentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUpdateCommentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUpdateCommentRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/deletecomment": {"delete": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteCommentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteCommentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteCommentRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/comments": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "taskId", "in": "query", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/submit": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.SubmitActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.SubmitActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.SubmitActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/documents": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "clientId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "actionItemId", "in": "query", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/update": {"put": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.BCPUpdateActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.BCPUpdateActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.BCPUpdateActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/details": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "taskId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem": {"delete": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/copydocumenttotask": {"post": {"tags": ["ActionItem"], "parameters": [{"name": "sourceTaskId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "sourceTaskGroupId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "sourceFileName", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "targetTaskId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "targetPath", "in": "query", "style": "form", "schema": {"type": "string", "default": "/Uploads"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/documentdirectory": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "taskId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "taskGroupId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/complete": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOCompleteActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOCompleteActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOCompleteActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/return": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOReturnActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOReturnActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOReturnActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/taskgroups": {"get": {"tags": ["ActionItem"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/taskgroup/create": {"post": {"tags": ["ActionItem"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "groupName", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/sign": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOSignActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOSignActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOSignActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/createsignaturerequest": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"IsPriority": {"type": "boolean"}, "SigningDocument": {"type": "string", "format": "binary"}, "EnvelopeId": {"type": "string"}, "EnableSigningOrder": {"type": "boolean"}, "actionItemDetails": {"type": "string"}}}, "encoding": {"IsPriority": {"style": "form"}, "SigningDocument": {"style": "form"}, "EnvelopeId": {"style": "form"}, "EnableSigningOrder": {"style": "form"}, "actionItemDetails": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/createdocumentrequest": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.BCPCreateDocumentActionItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.BCPCreateDocumentActionItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.BCPCreateDocumentActionItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/docusign/envelope": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"File": {"type": "string", "format": "binary"}, "signers": {"type": "string"}, "enableSigningOrder": {"type": "boolean"}}}, "encoding": {"File": {"style": "form"}, "signers": {"style": "form"}, "enableSigningOrder": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/docusign/envelope/edit": {"post": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.EditEnvelopeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.EditEnvelopeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.EditEnvelopeRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/docusign/envelope/signers": {"put": {"tags": ["ActionItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.UpdateEnvelopeSignersRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.UpdateEnvelopeSignersRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.UpdateEnvelopeSignersRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/completesigningrequest": {"post": {"tags": ["ActionItem"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "actionItemId", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/actionitem/uploaddocuments": {"post": {"tags": ["ActionItem"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "actionItemId", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "path", "in": "query", "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/login": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/exchange": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "code", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "state", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/auth/status": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Auth.AuthStatus"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Auth.AuthStatus"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Auth.AuthStatus"}}}}}}}, "/api/auth/logout": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/client": {"get": {"tags": ["Client"], "parameters": [{"name": "includeUserCount", "in": "query", "style": "form", "schema": {"type": "boolean", "default": true}}, {"name": "includeProjectCount", "in": "query", "style": "form", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Modules.Client.Response.ClientListResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Modules.Client.Response.ClientListResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Modules.Client.Response.ClientListResponse"}}}}}}}}, "/api/client/{id}/contacts": {"get": {"tags": ["Client"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOClientContact"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOClientContact"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOClientContact"}}}}}}}}, "/api/client/search": {"get": {"tags": ["Client"], "parameters": [{"name": "searchQuery", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "string", "default": ""}}, {"name": "memberFirmId", "in": "query", "style": "form", "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUser"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUser"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUser"}}}}}}}}, "/api/context": {"get": {"tags": ["Context"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}}}}}}, "/context": {"get": {"tags": ["Context"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUser"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUser"}}}}}}}, "/api/document/upload": {"post": {"tags": ["Document"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "path", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "folderListItemId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ContentType": {"type": "string"}, "ContentDisposition": {"type": "string"}, "Headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "FileName": {"type": "string"}}}, "encoding": {"ContentType": {"style": "form"}, "ContentDisposition": {"style": "form"}, "Headers": {"style": "form"}, "Length": {"style": "form"}, "Name": {"style": "form"}, "FileName": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/create": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.FolderCreationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.FolderCreationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.FolderCreationRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/manageaccess": {"put": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOManageAccessRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOManageAccessRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOManageAccessRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/move": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.MoveDocumentsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.MoveDocumentsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.MoveDocumentsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/downloadfile": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentDownloadRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentDownloadRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentDownloadRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["Document"], "parameters": [{"name": "downloadKey", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/document/foldercontent": {"get": {"tags": ["Document"], "parameters": [{"name": "path", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "filter", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentsResponse"}}}}}}}, "/api/document/recyclebin": {"get": {"tags": ["Document"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORecycleBinDocument"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORecycleBinDocument"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORecycleBinDocument"}}}}}}}}, "/api/document/deleterecyclebinitemspermanently": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteRecycleBinItemPermanentlyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteRecycleBinItemPermanentlyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDODeleteRecycleBinItemPermanentlyRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/movedocumentstorecyclebin": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/getavailableuserstoassignaccess": {"get": {"tags": ["Document"], "parameters": [{"name": "MemberFirmId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "ClientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "ProjectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "ItemId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "NewFolderPath", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/document/restoredocuments": {"post": {"tags": ["Document"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORestoreRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORestoreRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORestoreRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/rename": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORenameDocumentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORenameDocumentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORenameDocumentRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/updatepermissions": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUpdateFolderPermissionsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUpdateFolderPermissionsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUpdateFolderPermissionsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/document/getpermissions": {"get": {"tags": ["Document"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "memberFirmId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "itemId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/document/getdocumentdetailsbydriveitemid": {"get": {"tags": ["Document"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "driveItemId", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}}}}}}}, "/api/document/getdocumentdetailsbypath": {"get": {"tags": ["Document"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "path", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}}}}}}}, "/api/email/assets/{path}": {"get": {"tags": ["Email"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/email/preview/{path}": {"get": {"tags": ["Email"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/events": {"get": {"tags": ["Events"], "parameters": [{"name": "BatchIndex", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "BatchSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "EventNames", "in": "query", "style": "form", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.EventName"}}}, {"name": "From", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "To", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "UserIds", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.Event, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.Event, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.Event, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/events/{id}": {"get": {"tags": ["Events"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Event"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Event"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Event"}}}}}}}, "/api/events/logs": {"get": {"tags": ["Events"], "parameters": [{"name": "BatchIndex", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "BatchSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "EventIds", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, {"name": "Handlers", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Statuses", "in": "query", "style": "form", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.EventStatus"}}}, {"name": "MinRunNumber", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.EventLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.EventLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.EventLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/events/logs/{id}": {"get": {"tags": ["Events"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.EventLog"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.EventLog"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.EventLog"}}}}}}}, "/api/events/danger-purge-events": {"post": {"tags": ["Events"], "parameters": [{"name": "fromDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "confirmation", "in": "query", "style": "form", "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/health": {"get": {"tags": ["Health"], "responses": {"200": {"description": "Success"}}}}, "/api/jobs": {"get": {"tags": ["Jobs"], "parameters": [{"name": "BatchIndex", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "BatchSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "JobNames", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Statuses", "in": "query", "style": "form", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.JobStatus"}}}, {"name": "ScheduledOnly", "in": "query", "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.JobLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.JobLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Data.Models.JobLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/jobs/{id}": {"get": {"tags": ["Jobs"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.JobLog"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.JobLog"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.JobLog"}}}}}}}, "/api/locks": {"get": {"tags": ["Locks"], "responses": {"200": {"description": "Success"}}}}, "/api/locks/test": {"get": {"tags": ["Locks"], "responses": {"200": {"description": "Success"}}}}, "/api/locks/purge": {"post": {"tags": ["Locks"], "parameters": [{"name": "older<PERSON><PERSON><PERSON><PERSON>", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/notification": {"get": {"tags": ["Notification"], "parameters": [{"name": "BatchIndex", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "BatchSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "ProjectIds", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, {"name": "NotificationTypes", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "SortBy", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "ImportantOnly", "in": "query", "style": "form", "schema": {"type": "boolean"}}, {"name": "UnreadOnly", "in": "query", "style": "form", "schema": {"type": "boolean"}}, {"name": "NotificationTypesEnum", "in": "query", "style": "form", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.NotificationType"}}}, {"name": "SortDirectionEnum", "in": "query", "style": "form", "schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.SortDirectionEnum"}}, {"name": "SortByEnum", "in": "query", "style": "form", "schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.NotificationSortBy"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Core.Notifications.NotificationResponse, BCP.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Core.Notifications.NotificationResponse, BCP.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.BatchResult`1[[BCP.Core.Notifications.NotificationResponse, BCP.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/notification/projects": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}}}}}}}}, "/api/notification/updateimportance": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.UpdateImportanceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.UpdateImportanceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.UpdateImportanceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/notification/updatereadstatus": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.UpdateReadStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.UpdateReadStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.UpdateReadStatusRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/notification/getnotificationcount": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.NotificationCountResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.NotificationCountResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Notifications.NotificationCountResponse"}}}}}}}, "/api/onboarding/progress": {"get": {"tags": ["Onboarding"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.OnboardingProgress"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.OnboardingProgress"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.OnboardingProgress"}}}}}}}, "/api/onboarding/complete": {"post": {"tags": ["Onboarding"], "responses": {"200": {"description": "Success"}}}}, "/api/onboarding/acceptcookies": {"post": {"tags": ["Onboarding"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.AcceptCookieRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.AcceptCookieRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.AcceptCookieRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/onboarding/acceptterms": {"post": {"tags": ["Onboarding"], "responses": {"200": {"description": "Success"}}}}, "/api/onboarding/acceptprivacypolicy": {"post": {"tags": ["Onboarding"], "responses": {"200": {"description": "Success"}}}}, "/api/onboarding/termsacceptance": {"get": {"tags": ["Onboarding"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.TermsAcceptanceResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.TermsAcceptanceResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.TermsAcceptanceResponse"}}}}}}}, "/api/onboarding/privacypolicyacceptance": {"get": {"tags": ["Onboarding"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.PrivacyPolicyAcceptanceResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.PrivacyPolicyAcceptanceResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.PrivacyPolicyAcceptanceResponse"}}}}}}}, "/api/project": {"get": {"tags": ["Project"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}}}}}}}, "/api/project/editproject": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Project.EditProjectRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Project.EditProjectRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Project.EditProjectRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/project/client-projects": {"get": {"tags": ["Project"], "parameters": [{"name": "memberFirmId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/members": {"get": {"tags": ["Project"], "parameters": [{"name": "memberFirmId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/get-user-access": {"get": {"tags": ["Project"], "parameters": [{"name": "FirmId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "ClientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "Email", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "IsBdoGroup", "in": "query", "style": "form", "schema": {"type": "boolean"}}, {"name": "ProjectIds", "in": "query", "style": "form", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/edit-user": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Project.EditUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Project.EditUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Project.EditUserRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/project/rename": {"post": {"tags": ["Project"], "parameters": [{"name": "name", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/getprojectwithstages": {"get": {"tags": ["Project"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/getprojectstagebyid": {"get": {"tags": ["Project"], "parameters": [{"name": "projectStageId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/updateprojectstages": {"put": {"tags": ["Project"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/project/completeprojectstage": {"get": {"tags": ["Project"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "projectStageId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/projectcontacts": {"get": {"tags": ["Project"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/project/apt-linking-status": {"get": {"tags": ["Project"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/risk/library": {"get": {"tags": ["Risk"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Risks.Models.Risk"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Risks.Models.Risk"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Risks.Models.Risk"}}}}}}}}, "/api/risk": {"get": {"tags": ["Risk"], "parameters": [{"name": "projectId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectRisk"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectRisk"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectRisk"}}}}}}}, "post": {"tags": ["Risk"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOCreateProjectRiskRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOCreateProjectRiskRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOCreateProjectRiskRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Risk"], "parameters": [{"name": "projectRiskId", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/setting": {"get": {"tags": ["Setting"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.WebSettings"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.WebSettings"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Common.WebSettings"}}}}}}}, "/api/sync": {"post": {"tags": ["Sync"], "responses": {"200": {"description": "Success"}}}}, "/api/teammember/projectteammembers": {"get": {"tags": ["TeamMember"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/teammember/updateuserrole": {"post": {"tags": ["TeamMember"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.UpdateAccessRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.UpdateAccessRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.UpdateAccessRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/teammember/addusers": {"post": {"tags": ["TeamMember"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOAddUsersRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOAddUsersRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOAddUsersRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.User.AddUsersResponseResult"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.User.AddUsersResponseResult"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.User.AddUsersResponseResult"}}}}}}}}, "/api/teammember/removeuser": {"post": {"tags": ["TeamMember"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.RemoveBgpAccessRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.RemoveBgpAccessRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.RemoveBgpAccessRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/teammember/resendinvite": {"post": {"tags": ["TeamMember"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOReinviteUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOReinviteUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOReinviteUser"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/teammember/updateuser": {"put": {"tags": ["TeamMember"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/teammember/getmemberfirmusers": {"get": {"tags": ["TeamMember"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOSearchUser"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOSearchUser"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOSearchUser"}}}}}}}}, "/api/teammember/getportalusers": {"get": {"tags": ["TeamMember"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOPortalMember"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOPortalMember"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOPortalMember"}}}}}}}}, "/api/template/project/{guid}": {"get": {"tags": ["Template"], "parameters": [{"name": "guid", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ProjectTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ProjectTemplate"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ProjectTemplate"}}}}}}}, "/api/template/projects": {"get": {"tags": ["Template"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ProjectTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ProjectTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ProjectTemplate"}}}}}}}}, "/api/user/me": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}}}}}}, "/api/user/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}}}}}, "put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.User.UpdateUserRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}}}}}}, "/api/user/getcurrentuserrole": {"get": {"tags": ["User"], "parameters": [{"name": "clientId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "projectId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/user/byemail": {"get": {"tags": ["User"], "parameters": [{"name": "email", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}}}}}}, "/api/webhooks/client/{bgpClientId}": {"get": {"tags": ["Webhooks"], "parameters": [{"name": "bgpClientId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.Client"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.Client"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.Client"}}}}}}}, "/api/webhooks/crm/client/{externalReferenceId}": {"get": {"tags": ["Webhooks"], "parameters": [{"name": "externalReferenceId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Client"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Client"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Client"}}}}}}}, "/api/webhooks/client/{bgpClientId}/project/{bgpProjectId}": {"get": {"tags": ["Webhooks"], "parameters": [{"name": "bgpClientId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "bgpProjectId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.Project"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.Project"}}}}}}}, "/api/webhooks/crm/project/{externalReferenceId}": {"get": {"tags": ["Webhooks"], "parameters": [{"name": "externalReferenceId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}}}}}}}, "/api/webhooks/crm/createclientandprojects": {"post": {"tags": ["Webhooks"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.PlatformIntegrationCreateClientAndProjectRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.PlatformIntegrationCreateClientAndProjectRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.PlatformIntegrationCreateClientAndProjectRequestModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"BCP.Core.ActionItem.Models.BCPCreateDocumentActionItemRequest": {"type": "object", "properties": {"isPriority": {"type": "boolean"}, "actionItemDetails": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.CreateActionItemDetails"}}, "additionalProperties": false}, "BCP.Core.ActionItem.Models.BCPUpdateActionItemRequest": {"type": "object", "properties": {"taskId": {"type": "string", "format": "uuid"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "type": {"type": "string", "nullable": true}, "index": {"type": "integer", "format": "int32"}, "ownerUniqueUserId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "assigneeIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BCP.Core.ActionItem.Models.CreateActionItemDetails": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "type": {"type": "string", "nullable": true}, "ownerUniqueUserId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "assigneeIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "fileDestination": {"$ref": "#/components/schemas/BCP.Core.ActionItem.Models.FileDestination"}}, "additionalProperties": false}, "BCP.Core.ActionItem.Models.EditEnvelopeRequest": {"type": "object", "properties": {"envelopeId": {"type": "string", "nullable": true}, "returnUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.ActionItem.Models.FileDestination": {"type": "object", "properties": {"serverRelativeUrl": {"type": "string", "nullable": true}, "driveItemId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "listItemId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BCP.Core.ActionItem.Models.UpdateEnvelopeSignersRequest": {"type": "object", "properties": {"envelopeId": {"type": "string", "nullable": true}, "signers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "enableSigningOrder": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Auth.AuthStatus": {"type": "object", "properties": {"isAuthed": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest": {"type": "object", "properties": {"firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOAddCommentRequest": {"type": "object", "properties": {"firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOAddUsersRequest": {"type": "object", "properties": {"firmId": {"type": "integer", "format": "int32", "nullable": true}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "onboardingRedirectUrl": {"type": "string", "nullable": true}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOUserDetails"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOClientContact": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "loginName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "mobilePhone": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "uniqueUserId": {"type": "string", "nullable": true}, "profilePicture": {"nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOCompleteActionItemRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "firmId": {"type": "integer", "format": "int32", "nullable": true}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOCreateProjectRiskRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "impact": {"$ref": "#/components/schemas/BCP.Data.Models.Impact"}, "owner": {"$ref": "#/components/schemas/BCP.Data.Models.Owner"}, "strategy": {"type": "string", "nullable": true}, "projectId": {"type": "string", "format": "uuid"}, "templateId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDODeleteActionItemRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "memberFirmId": {"type": "integer", "format": "int32", "nullable": true}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDODeleteCommentRequest": {"type": "object", "properties": {"taskCommentId": {"type": "string", "nullable": true}, "firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDODeleteRecycleBinItemPermanentlyRequest": {"type": "object", "properties": {"memberFirmId": {"type": "integer", "format": "int32", "nullable": true}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "files": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOManageAccessRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "inherited": {"type": "boolean"}, "itemId": {"type": "integer", "format": "int32"}, "memberFirmId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32", "nullable": true}, "userIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashItem": {"type": "object", "properties": {"driveItemId": {"type": "string", "nullable": true}, "listItemId": {"type": "integer", "format": "int32"}, "path": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashItem"}, "nullable": true}, "folders": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashItem"}, "nullable": true}, "firmId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "readOnly": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOPortalMember": {"type": "object", "properties": {"uniqueUserId": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "userGroupName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "loginName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "isPending": {"type": "boolean"}, "redemptionUrl": {"type": "string", "nullable": true}, "invitationSentOn": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDORecycleBinDocument": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "actionItemName": {"type": "string", "nullable": true}, "originalLocation": {"type": "string", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "deletedAt": {"type": "string", "format": "date-time"}, "remainingDays": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOReinviteUser": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDORenameDocumentRequest": {"type": "object", "properties": {"memberFirmId": {"type": "integer", "format": "int32", "nullable": true}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "itemId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDORestoreItem": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDORestoreRequest": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.ControlAPI.Models.BDORestoreItem"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOReturnActionItemRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "firmId": {"type": "integer", "format": "int32", "nullable": true}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}, "extendedDueDate": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOSearchUser": {"type": "object", "properties": {"uniqueUserId": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "loginName": {"type": "string", "nullable": true}, "userGroupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOSignActionItemRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "memberFirmId": {"type": "integer", "format": "int32", "nullable": true}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}, "customReturnUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOUpdateCommentRequest": {"type": "object", "properties": {"taskCommentId": {"type": "string", "nullable": true}, "firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "taskId": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOUpdateFolderPermissionsRequest": {"type": "object", "properties": {"memberFirmId": {"type": "integer", "format": "int32", "nullable": true}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "itemId": {"type": "integer", "format": "int32"}, "inherited": {"type": "boolean"}, "userIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOUser": {"type": "object", "properties": {"uniqueUserId": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "mobilePhoneNumber": {"type": "string", "nullable": true}, "portalLanguage": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "userType": {"type": "string", "nullable": true}, "userPrincipalName": {"type": "string", "nullable": true}, "accountName": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "pronouns": {"type": "string", "nullable": true}, "viewedVirtualTour": {"type": "boolean"}, "firstLogin": {"type": "boolean"}, "accessType": {"type": "string", "nullable": true}, "fullName": {"nullable": true, "readOnly": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.BDOUserDetails": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "groupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.FolderCreationRequest": {"type": "object", "properties": {"memberFirmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "path": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.MoveDocumentsRequest": {"type": "object", "properties": {"itemIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "destinationFolderId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "memberFirmId": {"type": "integer", "format": "int32"}, "readOnly": {"type": "boolean", "nullable": true}, "isClient": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.ControlAPI.Models.SubmitActionItemRequest": {"type": "object", "properties": {"actionItemId": {"type": "string", "nullable": true}, "firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.BdoUser": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.Client": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "referenceId": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "documentFeatures": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.DmsExportSettings"}, "taskFeatures": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.DmsExportSettings"}, "Features": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ClientFeatures"}, "attributes": {"nullable": true}, "lastVisited": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "nullable": true}, "modifiedOn": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ClientCreationModel": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "crmId": {"type": "string", "nullable": true}, "bgpId": {"type": "integer", "format": "int32", "nullable": true}, "externalReferenceId": {"type": "string", "nullable": true}, "name": {"minLength": 1, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "projectCount": {"type": "integer", "format": "int32", "readOnly": true}, "requestedBy": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.BdoUser"}, "portalBdoAdmins": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.BdoUser"}, "nullable": true}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ProjectCreationModel"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ClientFeatures": {"type": "object", "properties": {"documents": {"type": "boolean"}, "tasks": {"type": "boolean"}, "exchangeArchive": {"type": "boolean"}, "teamManagement": {"type": "boolean"}, "customNavigations": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.KeyEnabledPair"}, "nullable": true}, "quickLinks": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.KeyEnabledPair"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ClientUser": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.DmsExportSettings": {"type": "object", "properties": {"exportToDmsEnabled": {"type": "boolean"}, "exportToDmsId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.KeyEnabledPair": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.PlatformIntegrationCreateClientAndProjectRequestModel": {"type": "object", "properties": {"client": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ClientCreationModel"}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.Project": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "referenceId": {"type": "string", "nullable": true}, "archivePeriodCustom": {"type": "boolean"}, "archivePeriodInDays": {"type": "integer", "format": "int32", "nullable": true}, "deletePeriodCustom": {"type": "boolean"}, "deletePeriodInDays": {"type": "integer", "format": "int32", "nullable": true}, "prospect": {"type": "boolean"}, "clientId": {"type": "integer", "format": "int32"}, "documentStructureTemplate": {"type": "integer", "format": "int32", "nullable": true}, "attributes": {"type": "object", "additionalProperties": {"nullable": true}, "nullable": true}, "features": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ProjectFeatures"}, "types": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ProjectTypes"}, "taskFeatures": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.DmsExportSettings"}, "documentFeatures": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.DmsExportSettings"}, "aptEngagementId": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "nullable": true}, "modifiedOn": {"type": "string", "nullable": true}, "lastVisited": {"type": "string", "nullable": true}, "archivedOn": {"type": "string", "nullable": true}, "archivedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ProjectCreationContact": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ProjectCreationModel": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "clientId": {"type": "string", "format": "uuid"}, "bgpId": {"type": "integer", "format": "int32", "nullable": true}, "templateId": {"type": "string", "nullable": true}, "externalReferenceId": {"type": "string", "nullable": true}, "name": {"minLength": 1, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "completionPercentage": {"type": "integer", "format": "int32"}, "defaultTaskGroupId": {"type": "string", "format": "uuid", "nullable": true}, "archived": {"type": "boolean"}, "statusText": {"type": "string", "nullable": true, "readOnly": true}, "stages": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectStage"}, "nullable": true}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectStatus"}, "newUsers": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ProjectCreationUsers"}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ProjectCreationUsers": {"type": "object", "properties": {"contacts": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ProjectCreationContact"}, "nullable": true}, "clientUsers": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.ClientUser"}, "nullable": true}, "bdoUsers": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.BdoUser"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ProjectFeatures": {"type": "object", "properties": {"documents": {"type": "boolean"}, "tasks": {"type": "boolean"}, "exchangeArchive": {"type": "boolean"}, "teamManagement": {"type": "boolean"}, "dmsExport": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.DmsExportSettings"}, "customNavigations": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.BGP.FirmAPI.KeyEnabledPair"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.BGP.FirmAPI.ProjectTypes": {"type": "object", "properties": {"confidential": {"type": "boolean"}, "prospect": {"type": "boolean"}, "internal": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Common.BatchResult`1[[BCP.Core.Notifications.NotificationResponse, BCP.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Notifications.NotificationResponse"}, "nullable": true}, "batchIndex": {"type": "integer", "format": "int32"}, "batchSize": {"type": "integer", "format": "int32"}, "batchLength": {"type": "integer", "format": "int32"}, "batchCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "BCP.Core.Common.BatchResult`1[[BCP.Data.Models.Event, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.Event"}, "nullable": true}, "batchIndex": {"type": "integer", "format": "int32"}, "batchSize": {"type": "integer", "format": "int32"}, "batchLength": {"type": "integer", "format": "int32"}, "batchCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "BCP.Core.Common.BatchResult`1[[BCP.Data.Models.EventLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.EventLog"}, "nullable": true}, "batchIndex": {"type": "integer", "format": "int32"}, "batchSize": {"type": "integer", "format": "int32"}, "batchLength": {"type": "integer", "format": "int32"}, "batchCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "BCP.Core.Common.BatchResult`1[[BCP.Data.Models.JobLog, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.JobLog"}, "nullable": true}, "batchIndex": {"type": "integer", "format": "int32"}, "batchSize": {"type": "integer", "format": "int32"}, "batchLength": {"type": "integer", "format": "int32"}, "batchCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "BCP.Core.Common.Features": {"type": "object", "properties": {"mimeTypeCheck": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Common.FileUploadSettings": {"type": "object", "additionalProperties": false}, "BCP.Core.Common.WebSettings": {"type": "object", "properties": {"redirectUri": {"type": "string", "nullable": true}, "telemetryConnStr": {"type": "string", "nullable": true}, "memberFirmId": {"type": "integer", "format": "int32"}, "measurementId": {"type": "string", "nullable": true}, "crmBaseUrl": {"type": "string", "nullable": true}, "fileUpload": {"$ref": "#/components/schemas/BCP.Core.Common.FileUploadSettings"}, "spoUrl": {"type": "string", "nullable": true}, "spoEnv": {"type": "string", "nullable": true}, "spoSitePattern": {"type": "string", "nullable": true, "readOnly": true}, "supportEmail": {"type": "string", "nullable": true}, "idleTimeout": {"type": "integer", "format": "int32"}, "features": {"$ref": "#/components/schemas/BCP.Core.Common.Features"}, "version": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "BCP.Core.Document.Models.DocumentActionItem": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Document.Models.DocumentDownloadRequest": {"type": "object", "properties": {"memberFirmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "files": {"type": "array", "items": {"type": "string"}, "nullable": true}, "folders": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.Document.Models.DocumentResponse": {"type": "object", "properties": {"fileKind": {"type": "string", "nullable": true}, "driveItemId": {"type": "string", "nullable": true}, "listItemId": {"type": "integer", "format": "int32"}, "displayName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "parentUrl": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "createdByName": {"type": "string", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedByName": {"type": "string", "nullable": true}, "restricted": {"type": "boolean"}, "readonly": {"type": "boolean"}, "requestApproval": {"type": "string", "nullable": true}, "requestSignature": {"type": "string", "nullable": true}, "deletionInProgress": {"type": "boolean"}, "actionItem": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentActionItem"}, "isTemplated": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Document.Models.DocumentsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Document.Models.DocumentResponse"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.Modules.Client.Response.ClientListResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "bgpId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "projectCount": {"type": "integer", "format": "int32"}, "userCount": {"type": "integer", "format": "int32"}, "externalReferenceId": {"type": "string", "nullable": true}, "industryName": {"type": "string", "nullable": true}, "industryId": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "dateCreated": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "practitionerCount": {"type": "integer", "format": "int32", "nullable": true}, "clientUserCount": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Modules.Onboarding.Models.AcceptCookieRequest": {"type": "object", "properties": {"consentType": {"$ref": "#/components/schemas/BCP.Core.Modules.Onboarding.Models.CookieConsentType"}}, "additionalProperties": false}, "BCP.Core.Modules.Onboarding.Models.CookieConsentType": {"type": "object", "properties": {"necessary": {"type": "boolean"}, "preference": {"type": "boolean"}, "statics": {"type": "boolean"}, "marketing": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Modules.Onboarding.Models.PrivacyPolicyAcceptanceResponse": {"type": "object", "properties": {"accepted": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Modules.Onboarding.Models.TermsAcceptanceResponse": {"type": "object", "properties": {"accepted": {"type": "boolean"}, "version": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Notifications.GenericEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Notifications.NotificationCountResponse": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BCP.Core.Notifications.NotificationResponse": {"type": "object", "properties": {"notificationId": {"type": "string", "format": "uuid"}, "user": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}, "actor": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}, "client": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}, "project": {"$ref": "#/components/schemas/BCP.Core.Notifications.GenericEntity"}, "payload": {"type": "string", "nullable": true}, "notificationType": {"$ref": "#/components/schemas/BCP.Data.Models.NotificationType"}, "createdAt": {"type": "string", "format": "date-time"}, "happenedAt": {"type": "string", "format": "date-time"}, "isImportant": {"type": "boolean"}, "readAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Notifications.NotificationSortBy": {"enum": ["happened_at"], "type": "string"}, "BCP.Core.Notifications.SortDirectionEnum": {"enum": ["desc", "asc"], "type": "string"}, "BCP.Core.Notifications.UpdateImportanceRequest": {"type": "object", "properties": {"notificationId": {"type": "string", "format": "uuid"}, "isImportant": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Notifications.UpdateReadStatusRequest": {"type": "object", "properties": {"notificationId": {"type": "string", "format": "uuid", "nullable": true}, "isRead": {"type": "boolean"}}, "additionalProperties": false}, "BCP.Core.Project.EditProjectRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectStatus"}, "completionPercentage": {"maximum": 100, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Project.EditUserRequest": {"type": "object", "properties": {"firmId": {"type": "integer", "format": "int32"}, "clientId": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "projectUserAccess": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Project.ProjectUserAccess"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.Project.ProjectUserAccess": {"type": "object", "properties": {"projectId": {"type": "integer", "format": "int32"}, "groupName": {"type": "string", "nullable": true}, "originalGroupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Risks.Models.Risk": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "nameLabel": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Template.Models.ActionItemTemplate": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Template.Models.FolderTemplate": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "subfolders": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.FolderTemplate"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.Template.Models.ProjectTemplate": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "stages": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.StageTemplate"}, "nullable": true}, "folders": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.FolderTemplate"}, "nullable": true}, "actionItems": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.ActionItemTemplate"}, "nullable": true}, "risks": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.RiskTemplate"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.Template.Models.RiskTemplate": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.Template.Models.StageTemplate": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "translationKey": {"type": "string", "nullable": true}, "sortingOrder": {"type": "integer", "format": "int32", "nullable": true}, "completionPercentageThreshold": {"type": "integer", "format": "int32", "nullable": true}, "substages": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Core.Template.Models.StageTemplate"}, "nullable": true}}, "additionalProperties": false}, "BCP.Core.User.AddUsersResponseResult": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/System.Net.HttpStatusCode"}, "bgpId": {"type": "string", "format": "uuid", "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BCP.Core.User.OnboardingProgress": {"type": "object", "properties": {"step": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BCP.Core.User.RemoveBgpAccessRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "firmId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BCP.Core.User.UpdateAccessRequest": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "memberFirmId": {"type": "integer", "format": "int32"}, "projectId": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "groupName": {"type": "array", "items": {"type": "string"}, "nullable": true}, "displayName": {"type": "string", "nullable": true}, "uniqueUserId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BCP.Core.User.UpdateUserRequest": {"type": "object", "properties": {"email": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "firstName": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "lastName": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "title": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "department": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "pronouns": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "phoneNumber": {"$ref": "#/components/schemas/Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "language": {"$ref": "#/components/schemas/Optional`1[[BCP.Data.Models.Language, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}, "onboardingStep": {"$ref": "#/components/schemas/Optional`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "emailFrequency": {"$ref": "#/components/schemas/Optional`1[[BCP.Data.Models.EmailFrequency, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}, "notificationTypes": {"$ref": "#/components/schemas/Optional`1[[System.Collections.Generic.List`1[[BCP.Data.Models.NotificationType, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "emailNotificationTypes": {"$ref": "#/components/schemas/Optional`1[[System.Collections.Generic.List`1[[BCP.Data.Models.NotificationType, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "businessDomains": {"$ref": "#/components/schemas/Optional`1[[System.Collections.Generic.List`1[[BCP.Data.Models.BusinessDomain, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "termsAccepted": {"$ref": "#/components/schemas/Optional`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "status": {"$ref": "#/components/schemas/Optional`1[[BCP.Data.Models.UserStatus, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]"}, "onboarded": {"$ref": "#/components/schemas/Optional`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}}, "additionalProperties": false}, "BCP.Data.Models.BusinessDomain": {"enum": ["economic_resiliency", "aritificial_intelligence", "investment_strategy_and_planning", "security_vulnerabilities_and_breaches", "digital_or_it_transformation", "fraud_protection", "mergers_and_acquisitions", "supply_chains", "office_three_six_five", "other"], "type": "string"}, "BCP.Data.Models.Client": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "crmId": {"type": "string", "nullable": true}, "bgpId": {"type": "integer", "format": "int32", "nullable": true}, "externalReferenceId": {"type": "string", "nullable": true}, "name": {"minLength": 1, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "projectCount": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "BCP.Data.Models.EmailFrequency": {"enum": ["none", "weekly"], "type": "string"}, "BCP.Data.Models.Event": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"$ref": "#/components/schemas/BCP.Data.Models.EventName"}, "payload": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "additionalProperties": false}, "BCP.Data.Models.EventLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "eventId": {"type": "string", "format": "uuid"}, "handler": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.EventStatus"}, "runNumber": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BCP.Data.Models.EventName": {"enum": ["none", "onboarding_completed", "project_changed", "client_changed", "action_item_changed", "document_added"], "type": "string"}, "BCP.Data.Models.EventStatus": {"enum": ["queued", "running", "done", "failed"], "type": "string"}, "BCP.Data.Models.Impact": {"enum": ["low", "medium", "high"], "type": "string"}, "BCP.Data.Models.JobLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "isScheduled": {"type": "boolean"}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.JobStatus"}, "stage": {"type": "string", "nullable": true}, "progress": {"type": "number", "format": "double"}, "detail": {"type": "string", "nullable": true}, "input": {"type": "string", "nullable": true}, "output": {"type": "string", "nullable": true}, "runNumber": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/BCP.Data.Models.User"}}, "additionalProperties": false}, "BCP.Data.Models.JobStatus": {"enum": ["queued", "running", "done", "failed"], "type": "string"}, "BCP.Data.Models.Language": {"enum": ["english", "french"], "type": "string"}, "BCP.Data.Models.NotificationType": {"enum": ["none", "onboarding_completed", "portal_invite", "project_invite", "client_admin_invite", "project_health_status_changed", "action_item_assigned", "action_item_status_changed", "action_item_comment_added", "final_delivery_added"], "type": "string"}, "BCP.Data.Models.Owner": {"enum": ["bdo", "client"], "type": "string"}, "BCP.Data.Models.Project": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "clientId": {"type": "string", "format": "uuid"}, "bgpId": {"type": "integer", "format": "int32", "nullable": true}, "templateId": {"type": "string", "nullable": true}, "externalReferenceId": {"type": "string", "nullable": true}, "name": {"minLength": 1, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "completionPercentage": {"type": "integer", "format": "int32"}, "defaultTaskGroupId": {"type": "string", "format": "uuid", "nullable": true}, "archived": {"type": "boolean"}, "statusText": {"type": "string", "nullable": true, "readOnly": true}, "stages": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectStage"}, "nullable": true}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectStatus"}}, "additionalProperties": false}, "BCP.Data.Models.ProjectRisk": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "projectId": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "strategy": {"type": "string", "nullable": true}, "templateId": {"type": "string", "nullable": true}, "impact": {"$ref": "#/components/schemas/BCP.Data.Models.Impact"}, "owner": {"$ref": "#/components/schemas/BCP.Data.Models.Owner"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BCP.Data.Models.ProjectStage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "kind": {"type": "string", "nullable": true}, "translationKey": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.StageStatus"}, "isEnabled": {"type": "boolean"}, "projectCompletion": {"type": "integer", "format": "int32", "nullable": true}, "startsAt": {"type": "string", "format": "date-time"}, "endsAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "childProjectStages": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.ProjectStage"}, "nullable": true}}, "additionalProperties": false}, "BCP.Data.Models.ProjectStatus": {"enum": ["ontrack", "needsattention", "atrisk"], "type": "string"}, "BCP.Data.Models.StageStatus": {"enum": ["complete", "in_progress", "delayed", "upcoming"], "type": "string"}, "BCP.Data.Models.User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "bgpId": {"type": "string", "format": "uuid"}, "ssoId": {"type": "string", "format": "uuid", "nullable": true}, "username": {"type": "string", "nullable": true, "readOnly": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true, "readOnly": true}, "title": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "pronouns": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "avatarColor": {"type": "string", "nullable": true}, "language": {"$ref": "#/components/schemas/BCP.Data.Models.Language"}, "onboardingStep": {"type": "integer", "format": "int32"}, "emailFrequency": {"$ref": "#/components/schemas/BCP.Data.Models.EmailFrequency"}, "notificationTypes": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.NotificationType"}, "nullable": true}, "emailNotificationTypes": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.NotificationType"}, "nullable": true}, "businessDomains": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.BusinessDomain"}, "nullable": true}, "termsAcceptedAt": {"type": "string", "format": "date-time", "nullable": true}, "termsVersion": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/BCP.Data.Models.UserStatus"}, "completedOnboardingAt": {"type": "string", "format": "date-time", "nullable": true}, "emailReminderSentAt": {"type": "string", "format": "date-time", "nullable": true}, "invitedAt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "clients": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.Client"}, "nullable": true, "readOnly": true}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.Project"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "BCP.Data.Models.UserStatus": {"enum": ["active", "inactive"], "type": "string"}, "Optional`1[[BCP.Data.Models.EmailFrequency, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"$ref": "#/components/schemas/BCP.Data.Models.EmailFrequency"}}, "additionalProperties": false}, "Optional`1[[BCP.Data.Models.Language, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"$ref": "#/components/schemas/BCP.Data.Models.Language"}}, "additionalProperties": false}, "Optional`1[[BCP.Data.Models.UserStatus, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"$ref": "#/components/schemas/BCP.Data.Models.UserStatus"}}, "additionalProperties": false}, "Optional`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Optional`1[[System.Collections.Generic.List`1[[BCP.Data.Models.BusinessDomain, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.BusinessDomain"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Optional`1[[System.Collections.Generic.List`1[[BCP.Data.Models.NotificationType, BCP.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"type": "array", "items": {"$ref": "#/components/schemas/BCP.Data.Models.NotificationType"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Optional`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "Optional`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"hasValue": {"type": "boolean", "readOnly": true}, "value": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "System.Net.HttpStatusCode": {"enum": ["continue", "switching_protocols", "processing", "early_hints", "ok", "created", "accepted", "non_authoritative_information", "no_content", "reset_content", "partial_content", "multi_status", "already_reported", "im_used", "multiple_choices", "moved_permanently", "found", "see_other", "not_modified", "use_proxy", "unused", "temporary_redirect", "permanent_redirect", "bad_request", "unauthorized", "payment_required", "forbidden", "not_found", "method_not_allowed", "not_acceptable", "proxy_authentication_required", "request_timeout", "conflict", "gone", "length_required", "precondition_failed", "request_entity_too_large", "request_uri_too_long", "unsupported_media_type", "requested_range_not_satisfiable", "expectation_failed", "misdirected_request", "unprocessable_entity", "locked", "failed_dependency", "upgrade_required", "precondition_required", "too_many_requests", "request_header_fields_too_large", "unavailable_for_legal_reasons", "internal_server_error", "not_implemented", "bad_gateway", "service_unavailable", "gateway_timeout", "http_version_not_supported", "variant_also_negotiates", "insufficient_storage", "loop_detected", "not_extended", "network_authentication_required"], "type": "string"}}, "securitySchemes": {"CustomAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Please enter your token", "name": "Authorization", "in": "header"}}}, "security": [{"CustomAuth": []}]}