{"AppConfig": null, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.Hosting.Lifetime": "Information", "BCP": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": ""}, "Auth": {"TenantId": "6449ec62-5df3-4c42-86a1-e590416d3059", "ClientId": "6e6abf1e-7ecc-476a-9c9d-b024351e74aa", "Audience": "aa995503-12e4-463d-830f-38c610f2620a", "RedirectUri": "http://localhost:5020"}, "ApplicationInsights": {"ConnectionString": ""}, "WebSettings": {"RedirectUri": "http://localhost:5020", "MemberFirmId": 36, "TelemetryConnStr": "", "MeasurementId": "", "CrmBaseUrl": "https://onesourcetemp.crm3.dynamics.com/?entityType=opportunity&entityId=", "SpoUrl": "https://bdogpaccemea.sharepoint.com/", "SpoEnv": "acc", "supportEmail": "<EMAIL>", "Features": {"MimeTypeCheck": true}, "idleTimeout": 30}, "WebhookAd": {"Authority": "https://login.microsoftonline.com/8906e8ca-1a9a-4306-8472-ad406c29942c", "Audience": "api://58d881b0-61a7-4804-9c69-304c1d925b3d"}, "FirmAPI": {"BaseUrlV3": "https://api.bdo.global/gpl-prvapi-acc-eur/v3", "BaseUrlV4": "https://api.bdo.global/gpl-firm-api-acc-eur/v4", "AppId": "81da3002-efab-497e-876d-f8f3d512e211", "AppSecret": "", "TenantId": "44f4e7a6-4821-44d7-b286-cd90436c6975", "SubscriptionKey": "", "AuthUrl": "https://login.microsoftonline.com/44f4e7a6-4821-44d7-b286-cd90436c6975/oauth2/v2.0/token", "Scope": "https://bdoapoutlook.onmicrosoft.com/bdo-web-prvapi-acc/.default", "MemberFirmId": 36, "UserOnboardingRedirectUrl": "http://localhost:5020"}, "PlatformIntegrationAPI": {"AuthUrl": "https://login.microsoftonline.com/ee7a07de-4778-4cfe-8e91-8d9038ba72ec/oauth2/v2.0/token", "AppId": "32d78cae-60ae-41ca-893b-a2ad89628b84", "AppSecret": "", "Scope": "https://apim-dev.app.bdo.ca/.default", "BaseUrl": "https://apim-dev.app.bdo.ca/esb-bcp-dev"}, "ControlAPI": {"ControlAPIBaseUrl": "https://gpl-ctrlapi-acc-eur.bdo.global", "MemberFirmId": 36}, "Orchestration": {"EnableOrchestrationBackgroundWorker": true, "ProcessingDelayInMilliseconds": 10000, "BgpPollingRetryLimitInSeconds": 600, "BgpPollingRetryTimeoutInSeconds": 15, "ServiceAccountName": "<EMAIL>"}, "Email": {"Key": "", "Sender": "<EMAIL>", "SenderName": "BDO Canada Local", "SandBox": true}, "Job": {"IsEnabled": false}, "Docusign": {"basePath": "https://demo.docusign.net/restapi", "oAuthBasePath": "account-d.docusign.com", "clientId": "90a8474d-b078-46dd-bfde-9595753ab7d1", "userId": "c30aadb2-9e95-4802-a831-3b3121649dfe", "accountId": "687dea33-deeb-47aa-8f6a-6e18a9b00e48", "privateKey": ""}}