import React, { useState, useEffect, SetStateAction } from "react";
import styles from "../sharedTableStyles.module.css";
import documentsTableStyles from "./documentsTable.module.css";
import { FileUpload } from "@ark-ui/react";
import {
  ActionItemChip,
  ButtonSizeEnum,
  ButtonTypeEnum,
  Checkbox,
  Chip,
  ChipDropdownItem,
  CustomAvatar,
  ChipType,
  DropdownMenu,
  Icon,
  IconName,
  Input,
  Modal,
  ModalSize,
  Spinner,
  Table,
  ColumnConfig,
  getDocumentsDateLabel,
  DropdownMenuItem,
  removeExtension,
  downloadFile,
  isReadonlyPath,
  getInitials,
} from "@bcp/uikit";
import {
  DocumentRow,
  documentTemplate,
  iconNameMapping,
  SortConfig,
  SortKey,
} from "../spec";
import {
  getFileExtension,
  getFileNameWithoutExtension,
  normalizeActionItemFileType,
  extractPathAfterDocuments,
  getPathFromNode,
  getSelectedFilesOrFolders,
  ConflictTypes,
  hasReadonly,
  hasRestricted,
  isR<PERSON>only,
  isReadonlyFolder,
  isTemplatedFolder,
  hasActiveSignatureRequest,
  generateFileDownloadUrl,
  getPathFromUrl,
} from "../utils";
import InfoBannerWrapper from "../info-banner/InfoBannerWrapper";

import { useSettingsService } from "~/services/settings";
import { BulkDownloadCard } from "../bulk-download/BulkDownloadCard";
import { DropdownInputItem } from "@bcp/uikit";
import classNames from "classnames";
import { useSearchParams, useOutletContext } from "react-router-dom";
import {
  INode,
  IMoveToTrashEntry,
  IMoveToTrashRequest,
  useDocumentService,
  IRenameRequest,
  MoveDocumentsResponse,
} from "~/services/document";
import { useProjectService } from "~/services/project";
import { useClientService } from "~/services/client";
import { useTranslation } from "react-i18next";
import { useRoleService } from "~/services/role";
import { DocumentDetails } from "./documentDetails";
import { useToastService } from "~/services/toast";
import { UrlUtils } from "~/utils/generic/urlUtils";
import { BulkMoveModal } from "../moveModal/BulkMoveModal";
import { SingleMoveModal } from "../moveModal/SingleMoveModal";
import { DocumentsOutletContext } from "../Documents";
import { AssigneeTooltip } from "@bcp/uikit";

interface DocumentsTableProps {
  files: DocumentRow[];
  uploadingFiles: string[];
  sortConfig: SortConfig;
  setSortConfig: (config: SortConfig) => void;
  onManageAccess: (row: DocumentRow) => void;
  selectedFileTypes: Set<ChipDropdownItem>;
  selectedUsers: Set<ChipDropdownItem>;
  selectedDates: Set<Date>;
  setSelectedRows: React.Dispatch<SetStateAction<string[]>>;
  setShowRestrictedBulkWarningModal: React.Dispatch<SetStateAction<boolean>>;
  setShowBulkMoveDocumentToFolderModal: React.Dispatch<SetStateAction<boolean>>;
  selectedRows: string[];
  isDownloadingDocuments: boolean;
  showRestrictedBulkWarningModal: boolean;
  showBulkMoveDocumentToFolderModal: boolean;
  setShowMoveDocumentsToRecycleModal: React.Dispatch<SetStateAction<boolean>>;
  showMoveDocumentsToRecycleModal: boolean;
  setIsDownloadingDocuments: React.Dispatch<SetStateAction<boolean>>;
  showMoveDocumentToFolderModal: boolean;
  setShowMoveDocumentToFolderModal: React.Dispatch<SetStateAction<boolean>>;
  showRestrictedWarningModal: boolean;
  setShowRestrictedWarningModal: React.Dispatch<SetStateAction<boolean>>;
  selectedMoveDocument: DocumentRow;
  setSelectedMoveDocument: React.Dispatch<SetStateAction<DocumentRow>>;
  handleSingleDocumentMoveModal: (item: DocumentRow) => void;
  setShowMoveDocumentModal: React.Dispatch<SetStateAction<boolean>>;
  showMoveDocumentModal: boolean;
  handleDocumentMoveToRecycleBin: (documents: DocumentRow[]) => void;
  handleFileUpload: (files: File[]) => Promise<void>;
}
//TODO: Developer note => Possibly refactor this and the other tables to also be a resuable component
const DocumentsTable: React.FC<DocumentsTableProps> = ({
  files,
  uploadingFiles,
  sortConfig,
  setSortConfig,
  onManageAccess,
  selectedFileTypes,
  selectedUsers,
  selectedDates,
  setSelectedRows,
  selectedRows,
  isDownloadingDocuments,
  setShowRestrictedBulkWarningModal,
  showRestrictedBulkWarningModal,
  showBulkMoveDocumentToFolderModal,
  setShowBulkMoveDocumentToFolderModal,
  setShowMoveDocumentsToRecycleModal,
  showMoveDocumentsToRecycleModal,
  setIsDownloadingDocuments,
  showMoveDocumentToFolderModal,
  setShowMoveDocumentToFolderModal,
  showRestrictedWarningModal,
  setShowRestrictedWarningModal,
  selectedMoveDocument,
  setSelectedMoveDocument,
  handleSingleDocumentMoveModal,
  setShowMoveDocumentModal,
  showMoveDocumentModal,
  handleDocumentMoveToRecycleBin,
  handleFileUpload,
}) => {
  const { t, i18n } = useTranslation("documents");
  const { t: tGlobal } = useTranslation("global");
  const { memberFirmId } = useSettingsService();
  const { activeProject, spoSite } = useProjectService();
  const { activeClient } = useClientService();
  const [documents, setDocuments] = useState<DocumentRow[]>(files);
  const [filteredData, setFilteredData] = useState<DocumentRow[]>(files);
  const [spoUrl, setSpoUrl] = useState("");
  const [
    selectedFolderToMoveDocument,
    setSelectedFolderToMoveDocument,
  ] = useState<INode | null>(null);
  const [
    selectedFoldersToMoveDocument,
    setSelectedFoldersToMoveDocument,
  ] = useState<INode[] | []>([]);

  const [uploadKey, setUploadKey] = useState(0);
  const [showRenameDocumentModal, setShowRenameDocumentModal] = useState(false);
  const [showRenameFolderModal, setShowRenameFolderModal] = useState(false);
  const [selectedRenamedDocument, setSelectedRenamedDocument] = useState<
    DocumentRow
  >(documentTemplate);
  const [selectedRenamedFolder, setSelectedRenamedFolder] = useState<
    DocumentRow
  >(documentTemplate);
  const [loadingDownloadItems, setLoadingDownloadItems] = useState<string[]>(
    []
  );

  const [renamedDocumentName, setRenamedDocumentName] = useState<string>("");
  const [renamedFolderName, setRenamedFolderName] = useState<string>("");
  const [inputError, setInputError] = useState<boolean>(false);
  const [inputErrorMessage, setInputErrorMessage] = useState<string>("");
  const [isRenamed, setIsRenamed] = useState(false);
  const {
    moveToTrash,
    rename,
    nodes,
    updateIsRestrictedFolder,
    moveNodes,
    fetchMoveFolderContent,
    moveDocuments,
    createDownloadFileAndFolders,
    updateFolderItemId,
    isCurrentFolderRestricted,
  } = useDocumentService();

  // TODO: move this state change to component
  const [isAddingNewFolder, setIsAddingNewFolder] = useState<boolean>(false);
  const [isRenamingDocument, setIsRenamingDocument] = useState<boolean>(false);
  const { isClient } = useRoleService();
  const { showToast } = useToastService();
  const settings = useSettingsService();

  const shouldShowAddFolderButton =
    selectedFoldersToMoveDocument.length === 1 &&
    !(
      isClient &&
      selectedFolderToMoveDocument &&
      isReadonlyFolder(selectedFolderToMoveDocument)
    );

  const { isSearchActive } = useOutletContext<DocumentsOutletContext>();

  const [modalActionLoading, setModalActionLoading] = useState<boolean>(false);
  const { reloadDocuments } = useOutletContext<{
    reloadDocuments: () => void;
  }>();

  const [_, setParams] = useSearchParams();

  useEffect(() => {
    setSpoUrl(settings.settings?.data?.spoUrl ?? "");
  }, [settings.ready]);

  useEffect(() => {
    setDocuments(files);
  }, [files]);

  const shouldDisableDragAndDropUpload =
    isClient && isReadonlyPath(getPathFromUrl());

  const filterFilesAndFolders = (
    data: DocumentRow[],
    sortConfig: SortConfig
  ) => {
    const folders = data.filter(
      entry => entry.folderOrFile === "Folder" && !entry.deletionInProgress
    );
    const files = data.filter(
      entry => entry.folderOrFile === "File" && !entry.deletionInProgress
    );

    const sortEntries = (entries: DocumentRow[]) => {
      return [...entries].sort((a, b) => {
        const { key, direction } = sortConfig;

        if (key === "modified") {
          const dateA = new Date(a.modifiedAt);
          const dateB = new Date(b.modifiedAt);

          // Ensure valid dates - if invalid, treat as very old date
          const timeA = isNaN(dateA.getTime()) ? 0 : dateA.getTime();
          const timeB = isNaN(dateB.getTime()) ? 0 : dateB.getTime();

          const compare = timeA - timeB;
          return direction === "asc" ? compare : -compare;
        }

        // Handle string-based sorting for title and actionItemName
        let valueA: string = "";
        let valueB: string = "";

        if (key === "title") {
          valueA = a.title ?? "";
          valueB = b.title ?? "";
        } else if (key === "actionItemName") {
          valueA = a.actionItemName ?? "";
          valueB = b.actionItemName ?? "";
        }

        const compare = valueA.localeCompare(valueB);
        return direction === "asc" ? compare : -compare;
      });
    };

    const sortedFolders = sortEntries(folders);
    const sortedFiles = sortEntries(files);

    return [...sortedFolders, ...sortedFiles];
  };

  useEffect(() => {
    setDocuments(filterFilesAndFolders(documents, sortConfig));
    setFilteredData(filterFilesAndFolders(documents, sortConfig));
  }, [sortConfig]);

  useEffect(() => {
    const applyFilters = () => {
      let filtered = [...documents];

      if (selectedFileTypes.size > 0) {
        filtered = filtered.filter(row =>
          Array.from(selectedFileTypes).some(
            item => item.label === row.fileType
          )
        );
      }

      if (selectedUsers.size > 0) {
        filtered = filtered.filter(row =>
          Array.from(selectedUsers).some(
            item => item.label === row.createdByName
          )
        );
      }

      if (selectedDates.size === 2) {
        const [startDate, endDate] = selectedDates;
        const endDateAddADay = new Date(endDate);
        endDateAddADay.setDate(endDateAddADay.getDate() + 1);

        filtered = filtered.filter(row => {
          const modifiedDate = new Date(row.modifiedAt);

          return (
            modifiedDate >= new Date(startDate) &&
            modifiedDate < new Date(endDateAddADay)
          );
        });
      }

      setFilteredData(filterFilesAndFolders(filtered, sortConfig));
    };

    applyFilters();
  }, [selectedFileTypes, selectedUsers, selectedDates, documents]);

  const isAllSelected =
    selectedRows.length === documents.length && documents.length > 0;

  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedRows(documents.map(row => row.id));
    } else {
      setSelectedRows([]);
    }
  };

  const handleRowSelect = (id: string, isSelected: boolean) => {
    setSelectedRows(prevSelected =>
      isSelected
        ? [...prevSelected, id]
        : prevSelected.filter(rowId => rowId !== id)
    );
  };

  const toggleSort = (key: SortKey) => {
    const newConfig: SortConfig = {
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === "asc"
          ? "desc"
          : "asc",
    };
    setSortConfig(newConfig);
  };

  const openDocument = (item: DocumentRow) => {
    if (!spoSite || !spoSite.url || !item.url) return;
    const sharepointBaseUrl =
      spoUrl.charAt(spoUrl.length - 1) == "/"
        ? spoUrl.substring(0, spoUrl.length - 1)
        : spoUrl;

    // PDF files should always be downloaded
    if (getFileExtension(item.title) === "pdf") {
      const downloadUrl = generateFileDownloadUrl(spoSite.url, item.url);
      downloadFile(downloadUrl, item.name);
      return;
    }
    const documentUrl = `${sharepointBaseUrl}${item.url}`;
    const viewDocumentUrl = getSharepointUrl(item, documentUrl);
    window.open(`${viewDocumentUrl}`, "_blank");
  };

  const getSharepointUrl = (item: DocumentRow, documentUrl: string) => {
    if (!spoSite || !spoSite.url || !item.url) return "";
    // PDF files should always be downloaded
    if (getFileExtension(item.title) === "pdf") {
      const downloadUrl = generateFileDownloadUrl(spoSite.url, item.url);
      return downloadUrl;
    }
    //Any Document in a Read-Only folder should open in view-only, not sharepoint editable link
    if (isReadonly(item)) {
      return documentUrl;
    }

    return `${documentUrl}?web=1`;
  };

  const checkValidName = (inputString: string, file: boolean): boolean => {
    const pattern = file ? /[.*:<>?\\/~|"';()]/ : /[*<>?\\/~|]|\.{1}$/;

    return pattern.test(inputString) || inputString.length >= 178;
  };

  const downloadItem = async (item: DocumentRow) => {
    if (!activeClient?.bgpId || !activeProject?.bgpId || !item.url) {
      console.error(
        `Client, project or item url not loaded when downloading item ${item.id}`
      );
      return;
    }
    if (item.folderOrFile == "Folder") {
      setLoadingDownloadItems([...loadingDownloadItems, item.id]);
      try {
        await createDownloadFileAndFolders(
          settings.memberFirmId,
          activeClient?.bgpId,
          activeProject?.bgpId,
          [],
          [item.url]
        );
      } catch (e) {
        console.error("failed to download item", e);
        showToast({
          message: t(`failed-to-download-item`, { path: item.url }),
          type: "error",
          persist: false,
        });
      } finally {
        setLoadingDownloadItems(
          loadingDownloadItems.filter(i => i === item.id)
        );
      }
    } else {
      const _downloadPath = `/_layouts/15/download.aspx?SourceUrl=${encodeURIComponent(
        item.url ?? ""
      )}`;
      const _url = UrlUtils.join(spoSite?.url, _downloadPath);
      downloadFile(_url, item.name);
    }
  };

  const selectedFilesAndFolders = getSelectedFilesOrFolders(
    selectedRows,
    documents
  );

  const getContextMenuActions = (item: DocumentRow): DropdownMenuItem[] => {
    const copyLinkAction: DropdownMenuItem = {
      value: "Copy link",
      label: t("copy-link"),
      iconName: "link-icon",
      labelSelection: "copy-link",
      ariaLabel: "Copy document link",
      role: "button",
      dataTestId: "uikit-button-copyFileLink",
    };

    const openFileAction: DropdownMenuItem = {
      value: item.url || "",
      label: t("open"),
      iconName: "open-icon",
      labelSelection: "open-document",
      ariaLabel: "Open document",
      role: "button",
      dataTestId: "uikit-button-openFile",
    };

    const renameFileAction: DropdownMenuItem = {
      value: "Rename",
      label: t("rename"),
      iconName: "rename-icon",
      ariaLabel: "Rename document",
      role: "button",
      criticalHandler: () => {
        setSelectedRenamedDocument(item);
        setShowRenameDocumentModal(true);
        setRenamedDocumentName(getFileNameWithoutExtension(item.title));
      },
      dataTestId: "uikit-button-renameFile",
    };

    const renameFolderAction: DropdownMenuItem = {
      value: "Rename",
      label: t("rename"),
      iconName: "rename-icon",
      ariaLabel: "Rename folder",
      role: "button",
      criticalHandler: () => {
        setSelectedRenamedFolder(item);
        setShowRenameFolderModal(true);
        setRenamedFolderName(item.title);
      },
      dataTestId: "uikit-button-renameFile",
    };

    const moveAction: DropdownMenuItem = {
      value: "move-file",
      label: t("move-file"),
      iconName: "move-document-file-icon",
      criticalHandler: () => {
        fetchMoveDocumentFolders("/");
        handleSingleDocumentMoveModal(item);
      },
      ariaLabel: "Move file",
      role: "button",
      dataTestId: "uikit-button-moveFile",
    };

    const recycleBinAction: DropdownMenuItem = {
      label: t("move-to-recycle-bin"),
      value: "Move to Recycle bin",
      iconName: "move-document-to-recycle-bin-icon",
      role: "button",
      isCritical: true,
      criticalHandler: () => {
        handleDocumentMoveToRecycleBin([item]);
      },
      ariaLabel: "Move file to recycle bin",
      dataTestId: "uikit-button-moveFileToRecycleBin",
    };

    const manageAccessAction: DropdownMenuItem = {
      label: t("manage-access"),
      value: "Manage access",
      iconName: "people-icon",
      labelSelection: "manage-access",
      role: "button",
      criticalHandler: () => {
        onManageAccess(item);
      },
      ariaLabel: "Manage access",
      dataTestId: "uikit-button-manageAccess",
    };

    const isFile = item.folderOrFile === "File";
    const isFolder = item.folderOrFile === "Folder";

    const items = [copyLinkAction];

    if (isTemplatedFolder(item)) {
      return items;
    }

    if (isClient) {
      if (isFile) {
        items.unshift(openFileAction);
      }

      if (!isReadonly(item)) {
        if (isFile) {
          if (!hasActiveSignatureRequest(item)) {
            items.push(renameFileAction);
          }
        }

        if (isFolder) {
          items.push(renameFolderAction);
        }

        items.push(moveAction, recycleBinAction);
      }
    }

    if (!isClient) {
      if (isFile) {
        items.unshift(openFileAction);

        if (!hasActiveSignatureRequest(item)) {
          items.push(renameFileAction);
        }
      }

      if (isFolder) {
        items.push(renameFolderAction);
      }

      items.push(moveAction, manageAccessAction, recycleBinAction);
    }

    return items;
  };

  const handleDocumentClick = (item: DocumentRow) => {
    if (!item.url) return;

    if (isReadonly(item)) {
      downloadFile(item.url, item.name);
    } else {
      openDocument(item);
    }
  };

  const columns: ColumnConfig<DocumentRow>[] = [
    {
      key: "select" as keyof DocumentRow,
      headerClass: "selectHeader",
      header: (
        <Checkbox
          id="select-all"
          ariaLabel={t("select-all", { count: filteredData.length })}
          value={isAllSelected}
          onChange={isSelected => handleSelectAll(isSelected)}
        />
      ),
      showFilterIcon: false,
      tableCellClass: "selectTableCell",
      render: (item: DocumentRow) => {
        return (
          <div className={styles.selectCell}>
            <Checkbox
              ariaLabel={t("select-single-for-download", { name: item.title })}
              id={`select-document-${item.id}`}
              value={selectedRows.includes(item.id)}
              onChange={isSelected => handleRowSelect(item.id, isSelected)}
            />
          </div>
        );
      },
    },
    {
      key: "title" as keyof DocumentRow,
      headerClass: "titleDocumentsHeader",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("title")}
        >
          {t("title")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "title"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
              altText={`Sort ${sortConfig.direction}`}
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "titleDocumentsTableCell",
      render: (item: DocumentRow) => {
        const normalizedFileType = normalizeActionItemFileType(item.fileType);
        const iconName =
          item.folderOrFile == "File"
            ? iconNameMapping[normalizedFileType]
            : "folder-icon-filled";
        return item.folderOrFile == "File" ? (
          <div className={styles.titleCell}>
            <div className={styles.selectDocumentCellIcon}>
              <Icon iconName={iconName} />
            </div>
            <div>
              <a
                href=""
                className={styles.titleCellName}
                onClick={e => {
                  e.preventDefault();
                  handleDocumentClick(item);
                }}
              >
                <span className={styles.truncate}>{item.title}</span>
                <div className={styles.titleOpenIcon}>
                  <Icon iconName={"open-icon"} />
                </div>
                {item.restricted ? (
                  <Icon
                    iconName={"lock-icon"}
                    altText={"restriced-document"}
                    size={12}
                  />
                ) : null}
              </a>
              <div className={styles.fileDetails}>
                <div>{item.fileType}</div>
                {item.createdByName && (
                  <>
                    <Icon iconName={"dot-icon"} altText={""} />
                    <span
                      className={classNames(styles.filesCell, styles.truncate)}
                    >
                      <span
                        className={documentsTableStyles.uploadedByPrefix}
                      ></span>
                      {item.createdByName}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.titleCell}>
            <div className={styles.selectCellIcon}>
              <Icon iconName={iconName} altText={""} />
            </div>
            <button
              className={styles.folderLink}
              onClick={() => {
                setParams({
                  path: getPathFromNode(item),
                });
                updateIsRestrictedFolder(item.restricted!);
                updateFolderItemId(item.listItemId);
              }}
            >
              <div className={styles.titleCell}>
                <div>
                  <div className={styles.truncate}>{item.title}</div>
                </div>
                {item.restricted ? (
                  <Icon iconName={"lock-icon"} size={12} />
                ) : null}
              </div>
            </button>
            {isReadonly(item) && (
              <div className={documentsTableStyles.readOnlyChip}>
                <Chip
                  text={t("read-only")}
                  iconName={"read-only"}
                  type={ChipType.READONLY}
                  iconHeight={8}
                  iconWidth={11.99}
                />
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: "actionItemName" as keyof DocumentRow,
      headerClass: "actionItemHeader",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("actionItemName")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("action-item") }
          )}
        >
          {t("action-item")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "actionItemName"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "actionItemTableCell",
      render: (item: DocumentRow) => {
        return (
          <div>
            {item.folderOrFile != "Folder" && item.actionItemName && (
              <div className={styles.actionItemCell}>
                <ActionItemChip
                  text={item.actionItemName ?? "N/A"}
                  iconName={"action-item-icon"}
                />
              </div>
            )}
            {item.folderOrFile == "Folder" && item.readonly && (
              <div className={styles.readOnlyChip}>
                <Chip
                  text={t("read-only")}
                  iconName={"read-only"}
                  type={ChipType.READONLY}
                  iconHeight={8}
                  iconWidth={11.99}
                ></Chip>
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: "modified" as keyof DocumentRow,
      headerClass: "modifiedHeader",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("modified")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("last-modified") }
          )}
        >
          {t("last-modified")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "modified"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "modifiedTableCell",
      render: (item: DocumentRow) => {
        if (!item.modifiedAt) {
          return;
        }

        const displayDate = getDocumentsDateLabel(
          item.modifiedAt,
          i18n.language
        );

        return (
          <div className={styles.modifiedCell}>
            {item.userAvatar ? (
              <img
                src={item.userAvatar}
                alt={item.user}
                className={styles.avatar}
              />
            ) : (
              item.modifiedByName && (
                <AssigneeTooltip
                  message={item.modifiedByName}
                  inputId={crypto.randomUUID()}
                  direction="down"
                  smallSize
                  withArrow={false}
                >
                  <CustomAvatar
                    initials={getInitials(item.modifiedByName)}
                    avatarSize="x-small"
                    fontSize="x-small"
                    type="monogram"
                  />
                </AssigneeTooltip>
              )
            )}
            {displayDate}
          </div>
        );
      },
    },
    {
      key: "actions" as keyof DocumentRow,
      header: <span className="sr-only">{tGlobal("actions")}</span>,
      headerClass: "documentActionsHeader",
      showFilterIcon: false,
      tableCellClass: "",
      render: (item: DocumentRow) => {
        const documentLink = item.url || "";
        return (
          <div className={styles.actions}>
            <DocumentDetails
              item={item}
              location={item.originalLocation || "N/A"}
              showManageAccess={!isClient}
              onManageAccess={() => onManageAccess(item)}
              show={false}
            />
            <div>
              {loadingDownloadItems.indexOf(item.id) >= 0 ? (
                <div
                  className={
                    documentsTableStyles.tableRowDownloadSpinnerContainer
                  }
                >
                  <Spinner isLoading={true} size={"large"} />
                </div>
              ) : (
                <button
                  className={styles.downloadButton}
                  onClick={async () => downloadItem(item)}
                  aria-label={t("download-file", { name: item.title })}
                >
                  <Icon iconName="arrow-download" size={20} />
                </button>
              )}
            </div>
            <div className={styles.menuButton}>
              <DropdownMenu
                id={item.id}
                dataTestId={"uikit-dropdownMenu-documentDropdown"}
                ariaLabel={t("dropdownmenu-file", { name: item.title })}
                items={getContextMenuActions(item)}
                onChange={async (dropdownInputItem: DropdownInputItem) => {
                  switch (dropdownInputItem.labelSelection) {
                    case "copy-link": {
                      const isFolder = item.folderOrFile == "Folder";
                      const currentUrl = window.location.href;

                      const sharepointBaseUrl =
                        spoUrl.charAt(spoUrl.length - 1) == "/"
                          ? spoUrl.substring(0, spoUrl.length - 1)
                          : spoUrl;

                      const itemUrl = isFolder
                        ? currentUrl.charAt(currentUrl.length - 1) == "/"
                          ? `${currentUrl}${item.name}`
                          : `${currentUrl}/${item.name}`
                        : `${sharepointBaseUrl}${documentLink}`;

                      const encodedItemUrl = itemUrl.replace(/ /g, "%20");
                      await navigator.clipboard.writeText(
                        isFolder
                          ? encodedItemUrl
                          : getSharepointUrl(item, encodedItemUrl)
                      );

                      showToast({
                        type: "success",
                        message: t("link-copied"),
                        persist: false,
                      });

                      break;
                    }
                    case "open-document": {
                      openDocument(item);
                      break;
                    }
                    case "manage-access": {
                      onManageAccess(item);
                      break;
                    }
                  }
                }}
              />
            </div>
          </div>
        );
      },
    },
  ];

  const handleMoveDocumentToRecycleBin = async () => {
    setDocuments(prevData =>
      prevData.filter(doc => doc.id !== selectedMoveDocument.id)
    );
    setShowMoveDocumentModal(false);
    setSelectedMoveDocument(documentTemplate);
    if (
      activeClient?.bgpId &&
      activeProject &&
      activeProject.bgpId &&
      spoSite
    ) {
      const request: IMoveToTrashRequest = {
        clientId: activeClient?.bgpId,
        files: [],
        folders: [],
        firmId: memberFirmId,
        projectId: activeProject?.bgpId,
        readonly: selectedMoveDocument.readonly!,
      };

      const _baseUrl = UrlUtils.join(spoSite.path, "/Documents") + "/";
      const necessaryUrl = selectedMoveDocument.url!.replace(_baseUrl, "");

      if (selectedMoveDocument.folderOrFile == "File") {
        request.files.push({
          driveItemId: selectedMoveDocument.driveItemId,
          listItemId: selectedMoveDocument.listItemId,
          path: necessaryUrl,
        } as IMoveToTrashEntry);
      } else {
        request.folders.push({
          driveItemId: selectedMoveDocument.driveItemId,
          listItemId: selectedMoveDocument.listItemId,
          path: necessaryUrl,
        } as IMoveToTrashEntry);
      }

      try {
        await moveToTrash(request);
        reloadDocuments();
        showToast({
          message: t("delete-info-toast", {
            fileName: selectedMoveDocument.title,
          }),
          persist: false,
          type: "info",
        });
      } catch (err) {
        console.error(err);
        showToast({
          message: t("failed-to-move-to-recycle-bin", {
            fileName: selectedMoveDocument.title,
          }),
          persist: false,
          type: "error",
        });
      }
    }
  };

  const handleBulkMoveToRecycleBin = async () => {
    if (!selectedFilesAndFolders.length) return;

    setDocuments(prevData =>
      prevData.filter(
        doc => !selectedFilesAndFolders.some(selected => selected.id === doc.id)
      )
    );
    setShowMoveDocumentsToRecycleModal(false);

    if (activeClient?.bgpId && activeProject?.bgpId && spoSite) {
      const request: IMoveToTrashRequest = {
        clientId: activeClient.bgpId,
        files: [],
        folders: [],
        firmId: memberFirmId,
        projectId: activeProject.bgpId,
        readonly: selectedFilesAndFolders.some(item => item.readonly),
      };

      selectedFilesAndFolders.forEach(item => {
        const _baseUrl = UrlUtils.join(spoSite.path, "/Documents") + "/";
        const necessaryUrl = item.url!.replace(_baseUrl, "");

        const entry: IMoveToTrashEntry = {
          driveItemId: item.driveItemId,
          listItemId: item.listItemId,
          path: necessaryUrl,
        };

        if (item.folderOrFile === "File") {
          request.files.push(entry);
        } else {
          request.folders.push(entry);
        }
      });

      try {
        showToast({
          message: t("move-to-recycle-bin-progress", {
            count: selectedFilesAndFolders.length,
          }),
          persist: false,
          type: "info",
        });
        await moveToTrash(request);
        showToast({
          message: t("move-to-recycle-bin-success", {
            count: selectedFilesAndFolders.length,
          }),
          persist: false,
          type: "success",
        });
      } catch (err) {
        console.error(err);
        showToast({
          message: t("failed-to-bulk-delete", {
            count: selectedFilesAndFolders.length,
          }),
          persist: false,
          type: "error",
        });
      }
      reloadDocuments();
    }
  };

  const handleRenameDocument = async () => {
    if (!activeClient || !activeProject) return;
    const trimmedName = renamedDocumentName.trim();
    const currentTitle = getFileNameWithoutExtension(
      selectedRenamedDocument.title.trim()
    );
    if (trimmedName === currentTitle) {
      setInputError(true);
      setInputErrorMessage(
        "The new document name cannot be the same as the current one."
      );
      return;
    }

    if (
      documents.some(
        document =>
          getFileNameWithoutExtension(document.title.trim().toLowerCase()) ===
          trimmedName.toLowerCase()
      )
    ) {
      setInputError(true);
      setInputErrorMessage(t("file-already-exists"));
      return;
    }

    setDocuments(prevData =>
      prevData.map(document =>
        document.id === selectedRenamedDocument.id
          ? { ...document, title: trimmedName }
          : document
      )
    );

    try {
      // Remove this once confirmed bgpId will always exist
      if (activeClient?.bgpId) {
        const extension = getFileExtension(
          selectedRenamedDocument.title.trim()
        );
        const payload: IRenameRequest = {
          itemId: selectedRenamedDocument.listItemId,
          name: `${trimmedName}.${extension}`,
          projectId: activeProject.bgpId,
          clientId: activeClient.bgpId,
        };
        setIsRenamingDocument(true);
        await rename(payload);
        setIsRenamingDocument(false);

        resetModalState();
        showToast({
          message: t("rename-success-toast", {
            curr: trimmedName,
            prev: currentTitle,
          }),
          persist: false,
          type: "success",
        });
        reloadDocuments();
      } else {
        console.error("Client BgpId is not set");
      }
    } catch (error) {
      console.error(error);
      showToast({
        message: t("failed-to-rename"),
        type: "error",
        persist: false,
      });
      setIsRenamingDocument(false);
    }
  };

  const handleRenameFolder = async () => {
    if (!activeClient || !activeProject) return;
    const trimmedName = renamedFolderName.trim();
    const currentTitle = getFileNameWithoutExtension(
      selectedRenamedFolder.title.trim()
    );

    if (trimmedName.length === 0) {
      setInputError(true);
      setInputErrorMessage(t("folder-name-is-required"));
      return;
    }

    if (trimmedName === currentTitle) {
      setInputError(true);
      setInputErrorMessage(
        "The new folder name cannot be the same as the current one."
      );
      return;
    }

    if (
      documents.some(
        folder =>
          getFileNameWithoutExtension(folder.title.trim().toLowerCase()) ===
          trimmedName.toLowerCase()
      )
    ) {
      setInputError(true);
      setInputErrorMessage(t("folder-name-exists"));
      return;
    }

    setDocuments(prevData =>
      prevData.map(folder =>
        folder.id === selectedRenamedFolder.id
          ? { ...folder, title: trimmedName }
          : folder
      )
    );
    setModalActionLoading(true);
    try {
      // Remove this once confirmed bgpId will always exist
      if (activeClient?.bgpId) {
        const payload: IRenameRequest = {
          itemId: selectedRenamedFolder.listItemId,
          name: `${trimmedName}`,
          projectId: activeProject.bgpId,
          clientId: activeClient.bgpId,
        };
        setIsRenamingDocument(true);
        await rename(payload);
        setIsRenamingDocument(false);
        showToast({
          message: t("rename-success-toast", {
            curr: trimmedName,
            prev: currentTitle,
          }),
          type: "success",
          persist: false,
        });
        resetModalState();
        reloadDocuments();
      } else {
        console.error("Client BgpId is not set");
      }
    } catch (error) {
      console.error(error);
      showToast({
        message: t("failed-to-rename"),
        type: "error",
        persist: false,
      });
      setIsRenamingDocument(false);
    }
    setModalActionLoading(false);
  };

  const handleRenameOnChange = (value: string) => {
    const trimmedValue = value.trim();
    setRenamedDocumentName(value);

    const newNameWithoutExtension = removeExtension(trimmedValue);
    const currentFileNameWithoutExtension = removeExtension(
      selectedRenamedDocument.title.trim()
    );

    let error = false;
    let errorMsg = "";

    if (trimmedValue === "") {
      error = true;
      errorMsg = t("document-name-is-required");
    } else if (checkValidName(trimmedValue, true)) {
      error = true;
      errorMsg = t("error-invalid-file-name-input");
    }

    setInputError(error);
    setInputErrorMessage(errorMsg);

    if (!error) {
      setInputError(false);
      setInputErrorMessage("");
    }

    setIsRenamed(
      !error &&
        newNameWithoutExtension.toLowerCase() !==
          currentFileNameWithoutExtension.toLowerCase()
    );
  };

  const handleRenameFolderOnChange = (value: string) => {
    const newValue = value.trim();
    setRenamedFolderName(value);
    if (
      newValue.toLowerCase() != selectedRenamedFolder.title.toLowerCase() &&
      !checkValidName(newValue, false)
    ) {
      setIsRenamed(true);
    } else {
      setIsRenamed(false);
    }

    if (newValue === "") {
      setInputError(true);
      setInputErrorMessage(t("folder-name-is-required"));
    } else if (checkValidName(value, false)) {
      setInputError(true);
      setInputErrorMessage(t("error-invalid-folder-name-input"));
    } else {
      setInputError(false);
      setInputErrorMessage("");
    }
  };

  //TODO: Combine with bulk move after code complete
  const handleMoveDocument = async () => {
    try {
      if (activeClient?.bgpId && activeProject?.bgpId) {
        const fileAlreadyExists = moveNodes?.data?.some(
          node =>
            node.name.toLocaleLowerCase() ===
            selectedMoveDocument.title.toLocaleLowerCase()
        );

        if (fileAlreadyExists) {
          showToast({
            type: "error",
            title: t("failed-to-move", {
              name: selectedMoveDocument.title,
            }),
            message: t("move-fail-message-exists", {
              skipped: selectedMoveDocument.title,
            }),
            persist: false,
          });
          setModalActionLoading(false);
          return;
        }

        const destinationFolderId =
          selectedFolderToMoveDocument?.listItemId || 0;
        const documentId = selectedMoveDocument.listItemId || 0;
        const destinationFolderName =
          selectedFolderToMoveDocument?.displayName || "Documents";

        const response = await moveDocuments(
          memberFirmId,
          activeClient?.bgpId,
          activeProject?.bgpId,
          destinationFolderId,
          [documentId],
          selectedMoveDocument.readonly!,
          isClient
        );
        const movedDocuments: MoveDocumentsResponse = {
          documents: response.documents ?? {},
        };

        const skippedRestrictedKeys: number[] = [];

        for (const [key, value] of Object.entries(movedDocuments.documents)) {
          if (value == ConflictTypes.FolderContainsRestrictedFiles) {
            skippedRestrictedKeys.push(Number(key));
          }
        }

        const skippedRestrictedNames = [selectedMoveDocument]
          .filter(f => skippedRestrictedKeys.includes(f.listItemId || -1))
          .map(f => f.title);

        const toastRestrictedMessage =
          skippedRestrictedNames.length > 0
            ? t("failed-to-move-restricted-message")
            : "";

        const toastMessage = toastRestrictedMessage;

        if (skippedRestrictedNames.length > 0) {
          let fullFailTitle = t("failed-to-move", {
            name: skippedRestrictedNames[0],
          });

          if (skippedRestrictedNames[0].toLowerCase() === "folder") {
            fullFailTitle = t("failed-to-move-restricted");
          }

          showToast({
            type: "error",
            title: fullFailTitle,
            message: toastMessage,
            persist: false,
          });
        } else {
          showToast({
            type: "success",
            message: t("move-success-single-title", {
              destinationFolder: destinationFolderName,
            }),
            persist: false,
            withCTA: true,
            ctaText: t("view-folder"),
            onCTAClick: () => {
              setParams({
                path: selectedFolderToMoveDocument?.url
                  ? getPathFromNode(selectedFolderToMoveDocument)
                  : "/",
              });
            },
          });
        }

        resetModalState();
        reloadDocuments();
      }
    } catch (error) {
      console.log(error);
      showToast({
        type: "error",
        message: t("failed-to-move", {
          name: selectedMoveDocument.title,
        }),
        persist: false,
      });
    } finally {
      setModalActionLoading(false);
    }
  };

  //TODO: Combine with move file after code complete
  const handleBulkMoveDocuments = async () => {
    try {
      if (activeClient?.bgpId && activeProject?.bgpId) {
        const fileIds = selectedFilesAndFolders
          .filter(d => d.folderOrFile === "File")
          .map(d => d.listItemId || 0);

        const folderIds = selectedFilesAndFolders
          .filter(d => d.folderOrFile === "Folder")
          .map(d => d.listItemId || 0);

        const destinationId =
          typeof selectedFolderToMoveDocument?.listItemId === "number"
            ? selectedFolderToMoveDocument.listItemId
            : 0;

        const destinationFolderName =
          selectedFolderToMoveDocument?.name || t("documents");

        const response = await moveDocuments(
          memberFirmId,
          activeClient.bgpId,
          activeProject.bgpId,
          destinationId,
          [...fileIds, ...folderIds],
          false,
          isClient
        );

        const movedDocuments: MoveDocumentsResponse = {
          documents: response.documents ?? {},
        };
        const values = Object.values(movedDocuments.documents);
        const total = selectedFilesAndFolders.length;

        const alreadyExistsDocuments = values.filter(
          val => val === ConflictTypes.FileAlreadyExists
        );
        const restrictedFilesDocuments = values.filter(
          val => val === ConflictTypes.FolderContainsRestrictedFiles
        );

        const successCount =
          total -
          alreadyExistsDocuments.length -
          restrictedFilesDocuments.length;

        const skippedExistsKeys: number[] = [];
        const skippedRestrictedKeys: number[] = [];

        for (const [key, value] of Object.entries(movedDocuments.documents)) {
          if (value == ConflictTypes.FileAlreadyExists) {
            skippedExistsKeys.push(Number(key));
          } else if (value == ConflictTypes.FolderContainsRestrictedFiles) {
            skippedRestrictedKeys.push(Number(key));
          }
        }

        const skippedExistsNames = selectedFilesAndFolders
          .filter(f => skippedExistsKeys.includes(f.listItemId || -1))
          .map(f => f.title);

        const skippedRestrictedNames = selectedFilesAndFolders
          .filter(f => skippedRestrictedKeys.includes(f.listItemId || -1))
          .map(f => f.title);

        const toastExistsMessage =
          skippedExistsNames.length === 1
            ? t("move-fail-message-exists", {
                skipped: skippedExistsNames[0],
              })
            : skippedExistsNames.length > 1
            ? t("move-fail-message-exists-multiple", {
                skipped: skippedExistsNames.length,
              })
            : "";

        const toastRestrictedMessage =
          skippedRestrictedNames.length > 0
            ? t("failed-to-move-restricted-message")
            : "";

        const toastMessage = [toastRestrictedMessage, toastExistsMessage]
          .filter(Boolean)
          .join("\n");

        const hasBothMessages = !!(
          toastRestrictedMessage && toastExistsMessage
        );

        if (successCount === 0) {
          const fullFailTitle =
            total === 1
              ? t("failed-to-move", {
                  name: skippedRestrictedNames[0] ?? skippedExistsNames[0],
                })
              : t("failed-to-move-bulk");

          showToast({
            type: "error",
            title: fullFailTitle,
            message: toastMessage,
            persist: false,
            errorMessageWithLineBreaks: hasBothMessages,
            replaceLineBreaksWithBulletPoints: hasBothMessages,
          });
        } else if (successCount < total) {
          const partialSuccessTitle = t("move-partial-success-title", {
            count: successCount,
            destinationFolder: destinationFolderName,
          });

          showToast({
            type: "success",
            title: partialSuccessTitle,
            message: toastMessage,
            persist: false,
            errorMessageWithLineBreaks: hasBothMessages,
            replaceLineBreaksWithBulletPoints: hasBothMessages,
            withCTA: true,
            ctaText: t("view-folder"),
            onCTAClick: () => {
              setParams({
                path: selectedFolderToMoveDocument?.url
                  ? getPathFromNode(selectedFolderToMoveDocument)
                  : "/",
              });
            },
          });
        } else if (successCount === total) {
          showToast({
            type: "success",
            message: t("move-success-multiple", {
              count: total,
              destinationFolder: destinationFolderName,
            }),
            persist: false,
            withCTA: true,
            ctaText: t("view-folder"),
            onCTAClick: () => {
              setParams({
                path: selectedFolderToMoveDocument?.url
                  ? getPathFromNode(selectedFolderToMoveDocument)
                  : "/",
              });
            },
          });
        }

        resetModalState();
        reloadDocuments();
      }
    } catch (err) {
      console.error(err);
      showToast({
        type: "error",
        message: t("failed-to-move-bulk", {
          count: selectedFilesAndFolders.length,
        }),
        persist: false,
      });
    } finally {
      setModalActionLoading(false);
    }
  };

  const resetModalState = () => {
    setSelectedRenamedDocument(documentTemplate);
    setSelectedRenamedFolder(documentTemplate);
    setRenamedDocumentName("");
    setRenamedFolderName("");
    setShowRenameDocumentModal(false);
    setShowRenameFolderModal(false);
    setInputError(false);
    setInputErrorMessage("");
    setIsRenamed(false);
    setShowMoveDocumentToFolderModal(false);
    setIsAddingNewFolder(false);
    setSelectedFoldersToMoveDocument([]);
    setSelectedMoveDocument(documentTemplate);
    setSelectedFolderToMoveDocument(null);
    setShowBulkMoveDocumentToFolderModal(false);
  };

  const fetchMoveDocumentFolders = async (path: string) => {
    if (activeClient?.bgpId) {
      await fetchMoveFolderContent(
        memberFirmId,
        activeClient.bgpId,
        path,
        activeProject?.bgpId
      );
    }
  };

  //TODO: Move to refactored singular component
  const fetchSubFolders = async (folder: INode) => {
    const path = extractPathAfterDocuments(folder.parentUrl);
    setSelectedFolderToMoveDocument(folder);
    setSelectedFoldersToMoveDocument(prevState => {
      if (
        prevState.length === 0 ||
        prevState[prevState.length - 1].listItemId !== folder.listItemId
      ) {
        return [...prevState, folder];
      }
      return prevState;
    });
    fetchMoveDocumentFolders(`${path}/${folder.name}`);
  };

  const restrictedDocumentsToRecycleBinInfoBanner = (
    <InfoBannerWrapper
      text={t("move-restricted-recycle-bin-title")}
      message={t("move-restricted-recycle-bin-message")}
    />
  );
  const readOnlyDocumentsToRecycleBinInfoBanner = (
    <InfoBannerWrapper
      text={t("move-to-recycle-bin-read-only-title")}
      message={t("move-to-recycle-bin-read-only-message")}
    />
  );

  const isRecycleBinModalContentVisible =
    hasReadonly(selectedFilesAndFolders) ||
    hasRestricted(selectedFilesAndFolders) ||
    isCurrentFolderRestricted!;

  return (
    <>
      <div
        className={classNames(styles.container, documentsTableStyles.container)}
      >
        <FileUpload.Root
          key={uploadKey}
          className={classNames(documentsTableStyles.tableDropzone)}
          maxFiles={200}
          onFileChange={async details => {
            if (shouldDisableDragAndDropUpload) {
              showToast({
                type: "error",
                message: t("drag-and-drop-disabled"),
                persist: false,
              });
              return;
            }

            if (details.acceptedFiles.length > 0) {
              await handleFileUpload(details.acceptedFiles);
              setUploadKey(prev => prev + 1);
            }
          }}
          disabled={isSearchActive}
        >
          <FileUpload.Dropzone>
            {
              <Table
                columns={columns}
                data={filteredData}
                placeHolderItems={uploadingFiles}
                className={classNames(
                  documentsTableStyles.tableContainer,
                  "dropdown-menu-container"
                )}
                isLoading={nodes?.isBusy}
                shouldRenderDocumentsSkeleton
                emptyMessage={
                  isSearchActive
                    ? t("empty-folder-search")
                    : t("drag-files-here")
                }
                emptySubMessage={
                  isSearchActive
                    ? t("empty-folder-search-sub-message")
                    : t("drag-files-here-sub-message")
                }
                emptyMessageIcon="add-document-icon"
              />
            }
          </FileUpload.Dropzone>
        </FileUpload.Root>
        <Modal
          id="rename-document-modal"
          dataTestId="uikit-modal-renameDocument"
          title={t("rename-document")}
          size={ModalSize.SMALL}
          isVisible={showRenameDocumentModal}
          hide={() => {
            resetModalState();
          }}
          allowOverflow={false}
          primaryBtnConfig={{
            label: tGlobal("save"),
            onClick: handleRenameDocument,
            disabled:
              !isRenamed ||
              !renamedDocumentName.trim() ||
              isRenamingDocument ||
              checkValidName(renamedDocumentName, true),
            withRightIcon: false,
            type: ButtonTypeEnum.primary,
            size: ButtonSizeEnum.large,
            role: "button",
            ariaLabel: "Save name",
            dataTestId: "uikit-button-saveName",
          }}
          secondaryBtnConfig={{
            label: tGlobal("cancel"),
            onClick: () => {
              resetModalState();
            },
            disabled: isRenamingDocument,
            withRightIcon: false,
            type: ButtonTypeEnum.tertiary,
            size: ButtonSizeEnum.large,
            role: "button",
            ariaLabel: "Cancel rename",
            dataTestId: "uikit-button-cancelRename",
          }}
        >
          <div className={styles.slottedContainer}>
            <Input
              ariaLabel={renamedDocumentName}
              dataTestId="uikit-input-renameDocument"
              inputId={"renameDocument"}
              placeholder={t("document-name")}
              value={renamedDocumentName}
              error={inputError}
              errorMessage={inputErrorMessage}
              onValueChange={v => {
                handleRenameOnChange(v);
              }}
              errorMessageWithLineBreaks={true}
              replaceLineBreaksWithBulletPoints={true}
            />
            <span className={styles.inputFileType}>
              .{selectedRenamedDocument.fileType}
            </span>
          </div>
        </Modal>
        <Modal
          id="rename-folder-modal"
          dataTestId="uikit-modal-renameFolder"
          title={
            selectedRenamedFolder.folderOrFile.toLowerCase() === "file"
              ? t("rename-file")
              : t("rename-folder")
          }
          size={ModalSize.SMALL}
          isVisible={showRenameFolderModal}
          hide={() => {
            resetModalState();
          }}
          allowOverflow={false}
          primaryBtnConfig={{
            label: tGlobal("save"),
            onClick: handleRenameFolder,
            disabled:
              !isRenamed ||
              !renamedFolderName.trim() ||
              isRenamingDocument ||
              checkValidName(renamedDocumentName, false),
            withRightIcon: false,
            type: ButtonTypeEnum.primary,
            size: ButtonSizeEnum.large,
            role: "button",
            ariaLabel: "Save name",
            dataTestId: "uikit-button-saveName",
            loading: modalActionLoading,
          }}
          secondaryBtnConfig={{
            label: tGlobal("cancel"),
            onClick: () => {
              resetModalState();
            },
            disabled: isRenamingDocument,
            withRightIcon: false,
            type: ButtonTypeEnum.tertiary,
            size: ButtonSizeEnum.large,
            role: "button",
            ariaLabel: "Cancel rename",
            dataTestId: "uikit-button-cancelRename",
          }}
        >
          <div className={styles.slottedContainer}>
            <Input
              ariaLabel={renamedFolderName}
              dataTestId="uikit-input-renameDocument"
              inputId={"renameDocument"}
              placeholder={
                selectedRenamedFolder.folderOrFile.toLowerCase() === "file"
                  ? t("file-name")
                  : t("folder-name")
              }
              value={renamedFolderName}
              error={inputError}
              errorMessage={inputErrorMessage}
              onValueChange={v => {
                handleRenameFolderOnChange(v);
              }}
              errorMessageWithLineBreaks={true}
              replaceLineBreaksWithBulletPoints={true}
            />
          </div>
        </Modal>
        <SingleMoveModal
          selectedMoveDocument={selectedMoveDocument}
          selectedFolderToMoveDocument={selectedFolderToMoveDocument}
          isAddingNewFolder={isAddingNewFolder}
          shouldShowAddFolderButton={shouldShowAddFolderButton}
          setSelectedFolderToMoveDocument={setSelectedFolderToMoveDocument}
          setSelectedFoldersToMoveDocument={setSelectedFoldersToMoveDocument}
          setIsAddingNewFolder={setIsAddingNewFolder}
          fetchSubFolders={fetchSubFolders}
          fetchMoveDocumentFolders={fetchMoveDocumentFolders}
          selectedFoldersToMoveDocument={selectedFoldersToMoveDocument}
          showMoveDocumentToFolderModal={showMoveDocumentToFolderModal}
          setModalActionLoading={setModalActionLoading}
          resetModalState={resetModalState}
          handleMoveDocument={handleMoveDocument}
          modalActionLoading={modalActionLoading}
        />
        <BulkMoveModal
          selectedFilesAndFolders={selectedFilesAndFolders}
          selectedFolderToMoveDocument={selectedFolderToMoveDocument}
          isAddingNewFolder={isAddingNewFolder}
          shouldShowAddFolderButton={shouldShowAddFolderButton}
          setSelectedFolderToMoveDocument={setSelectedFolderToMoveDocument}
          setSelectedFoldersToMoveDocument={setSelectedFoldersToMoveDocument}
          setIsAddingNewFolder={setIsAddingNewFolder}
          fetchSubFolders={fetchSubFolders}
          fetchMoveDocumentFolders={fetchMoveDocumentFolders}
          selectedFoldersToMoveDocument={selectedFoldersToMoveDocument}
          showBulkMoveDocumentToFolderModal={showBulkMoveDocumentToFolderModal}
          setModalActionLoading={setModalActionLoading}
          resetModalState={resetModalState}
          handleBulkMoveDocuments={handleBulkMoveDocuments}
          modalActionLoading={modalActionLoading}
        />
        <Modal
          id="confirm-delete-modal"
          title={
            selectedMoveDocument.folderOrFile.toLocaleLowerCase() === "file"
              ? t("confirm-file-deletion")
              : t("confirm-folder-deletion")
          }
          subtitle={
            selectedMoveDocument.folderOrFile.toLocaleLowerCase() === "file"
              ? t("confirm-file-deletion-sub")
              : t("confirm-folder-deletion-sub")
          }
          size={ModalSize.SMALL}
          isVisible={showMoveDocumentModal}
          dataTestId={"uikit-modal-recycleBinModal"}
          hide={() => {
            setShowMoveDocumentModal(false);
          }}
          allowOverflow={false}
          primaryBtnConfig={{
            label: t("move-to-recycle-bin"),
            onClick: handleMoveDocumentToRecycleBin,
            disabled: false,
            withRightIcon: false,
            type: ButtonTypeEnum.primary,
            size: ButtonSizeEnum.large,
            ariaLabel: "Confirm moving to recycle bin",
            role: "button",
            dataTestId: "uikit-button-confirmMove",
          }}
          secondaryBtnConfig={{
            label: tGlobal("cancel"),
            onClick: () => {
              setShowMoveDocumentModal(false);
            },
            disabled: false,
            withRightIcon: false,
            type: ButtonTypeEnum.tertiary,
            size: ButtonSizeEnum.large,
            ariaLabel: "Cancel moving to recycle bin",
            role: "button",
            dataTestId: "uikit-button-cancelMove",
          }}
        >
          {(hasRestricted(selectedFilesAndFolders) ||
            isCurrentFolderRestricted) && (
            <div className={styles.slottedContainer}>
              {restrictedDocumentsToRecycleBinInfoBanner}
            </div>
          )}

          {hasReadonly(selectedFilesAndFolders) && (
            <div className={styles.slottedContainer}>
              {readOnlyDocumentsToRecycleBinInfoBanner}
            </div>
          )}
          <div className={styles.slottedContainer}>
            <div className={styles.documentItem}>
              <div className={styles.historyItem}>
                <div className={styles.leftHandInfo}>
                  <div className={styles.leftIconWrapper}>
                    <Icon
                      iconName="documents-icon"
                      altText="document-icon"
                      size={24}
                    />
                  </div>
                  <div className={styles.documentName}>
                    {selectedMoveDocument.title}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Modal>
        <Modal
          id="confirm-bulk-move-recycle-bin-modal"
          title={t("move-bulk-to-recycle-bin-title", {
            count: selectedFilesAndFolders.length,
          })}
          subtitle={t("move-bulk-to-recycle-bin-message", {
            count: selectedFilesAndFolders.length,
          })}
          size={ModalSize.SMALL}
          isVisible={showMoveDocumentsToRecycleModal}
          dataTestId={"uikit-modal-recycleBinModal"}
          ariaLabel={`Move ${selectedFilesAndFolders.length} items to recycle bin confirmation pop up`}
          hide={() => {
            setShowMoveDocumentsToRecycleModal(false);
          }}
          allowOverflow={false}
          primaryBtnConfig={{
            label: t("move-to-recycle-bin"),
            onClick: handleBulkMoveToRecycleBin,
            disabled: false,
            withRightIcon: false,
            type: ButtonTypeEnum.primary,
            size: ButtonSizeEnum.large,
            ariaLabel: "Confirm moving to recycle bin",
            role: "button",
            dataTestId: "uikit-button-confirmMove",
          }}
          secondaryBtnConfig={{
            label: tGlobal("cancel"),
            onClick: () => {
              setShowMoveDocumentsToRecycleModal(false);
            },
            disabled: false,
            withRightIcon: false,
            type: ButtonTypeEnum.tertiary,
            size: ButtonSizeEnum.large,
            ariaLabel: "Cancel moving to recycle bin",
            role: "button",
            dataTestId: "uikit-button-cancelMove",
          }}
          includeHeaderBorder={isRecycleBinModalContentVisible}
        >
          {(hasRestricted(selectedFilesAndFolders) ||
            isCurrentFolderRestricted) && (
            <div className={styles.slottedContainer}>
              {restrictedDocumentsToRecycleBinInfoBanner}
            </div>
          )}

          {hasReadonly(selectedFilesAndFolders) && (
            <div className={styles.slottedContainer}>
              {readOnlyDocumentsToRecycleBinInfoBanner}
            </div>
          )}
        </Modal>
        <Modal
          id="move-restricted-items-modal"
          title={t("move-restricted-items-title")}
          subtitle={t("move-restricted-items-message")}
          size={ModalSize.SMALL}
          isVisible={
            showRestrictedWarningModal || showRestrictedBulkWarningModal
          }
          dataTestId={"uikit-modal-restrictedWarningModal"}
          ariaLabel={t("move-restricted-items-title")}
          hide={() => {
            setShowRestrictedWarningModal(false);
            setShowRestrictedBulkWarningModal(false);
          }}
          allowOverflow={false}
          primaryBtnConfig={{
            label: tGlobal("continue"),
            onClick: () => {
              setShowRestrictedWarningModal(false);
              setShowRestrictedBulkWarningModal(false);
              if (selectedMoveDocument.id) {
                setShowMoveDocumentToFolderModal(true);
              } else {
                setShowBulkMoveDocumentToFolderModal(true);
              }
            },
            disabled: false,
            withRightIcon: false,
            type: ButtonTypeEnum.primary,
            size: ButtonSizeEnum.large,
            ariaLabel: "Confirm move restricted",
            role: "button",
            dataTestId: "uikit-button-confirmMoveRestricted",
          }}
          secondaryBtnConfig={{
            label: tGlobal("cancel"),
            onClick: () => {
              setShowRestrictedWarningModal(false);
              setShowRestrictedBulkWarningModal(false);
            },
            disabled: false,
            withRightIcon: false,
            type: ButtonTypeEnum.tertiary,
            size: ButtonSizeEnum.large,
            ariaLabel: "Cancel moving restricted files",
            role: "button",
            dataTestId: "uikit-button-cancelMoveRestricted",
          }}
        ></Modal>
        {isDownloadingDocuments && (
          <div className={styles.bulkDownloadModalWrapper}>
            <BulkDownloadCard
              selectedRows={selectedRows.length}
              setIsDownloadingDocuments={setIsDownloadingDocuments}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default DocumentsTable;
