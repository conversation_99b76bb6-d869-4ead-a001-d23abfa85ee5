using System.Linq.Expressions;
using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.BGP.ControlAPI.Spec;
using BCP.Core.Client;
using BCP.Core.Common;
using BCP.Core.Events;
using BCP.Core.Template.Models;
using BCP.Core.Template.Spec;
using BCP.Data;
using BCP.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking.Internal;
using EventPaylods = BCP.Core.Events.Payloads;
using ProjectModel = BCP.Data.Models.Project;
using ProjectStageModel = BCP.Data.Models.ProjectStage;

namespace BCP.Core.Project
{
    public class ProjectService : IProjectService
    {
        private readonly DataContext _context;
        private readonly IControlAPIService _bgpService;
        private readonly IClientService _clientService;
        private readonly IEventService _eventService;
        private readonly ITemplateService _templateService;
        private readonly IFirmProjectService _firmProjectService;

        public ProjectService(
            DataContext context,
            IControlAPIService bgpService,
            IClientService clientService,
            IEventService eventService,
            ITemplateService templateService,
            IFirmProjectService firmProjectService
        )
        {
            _context = context;
            _bgpService = bgpService;
            _clientService = clientService;
            _eventService = eventService;
            _templateService = templateService;
            _firmProjectService = firmProjectService;
        }

        public async Task<IEnumerable<ProjectModel>> GetUserProjects(Guid clientId)
        {
            var client = await _clientService.Get(clientId);
            if (client?.BGPId == null)
                throw new CoreException(CoreError.NotFound, "Client not found");
            var bgpProjects = await _bgpService.GetAccessibleProjects((int)client?.BGPId!);
            var bgpIds = bgpProjects?.Select(x => x.Id).ToArray();
            if (bgpIds == null)
                return [];
            object[] includes =
            {
                (Expression<Func<ProjectModel, object>>)(x => x.Stages), // Lambda expression
                "Stages.ChildProjectStages",
            };
            var projects = await this.GetByBgpId((int)client.BGPId, bgpIds, includes);
            return projects.OrderBy(project => project.Name);
        }

        public async Task<IEnumerable<BDOClientProjectResponse>> GetUserAccessForProjectsAsync(
            GetUserAccessForProjectsRequest request
        )
        {
            return await _bgpService.GetUserAccessForProjectsAsync(request);
        }

        public async Task<BCP.Core.BGP.FirmAPI.Project?> GetBgpProject(
            int bgpClientId,
            int bgpProjectId
        )
        {
            return await _firmProjectService.GetBgpProject(bgpClientId, bgpProjectId);
        }

        public async Task<bool> AddOrRemoveUserAsync(EditUserRequest request)
        {
            return await _bgpService.EditUserAsync(request);
        }

        public async Task<ProjectModel?> Get(Guid id, params object[] includes)
        {
            var projects = await Get([id], includes);
            return projects.FirstOrDefault();
        }

        public async Task<IEnumerable<ProjectModel>> Get(Guid[] ids, params object[] includes)
        {
            var query = _context.Projects.Where(x => ids.Contains(x.Id));
            foreach (var include in includes ?? [])
            {
                if (typeof(string) == include.GetType())
                {
                    query = query.Include((string)include);
                }
                else
                {
                    query = query.Include((Expression<Func<ProjectModel, object>>)include);
                }
            }

            return await query.ToListAsync();
        }

        public async Task<ProjectModel?> GetByBgpId(
            int bgpClientId,
            int bgpProjectId,
            params object[] includes
        )
        {
            var projects = await GetByBgpId(bgpClientId, [bgpProjectId], includes);
            return projects.FirstOrDefault();
        }

        public async Task<IEnumerable<ProjectModel>> GetByBgpId(
            int bgpClientId,
            int[] bgpProjectIds,
            params object[] includes
        )
        {
            var client = await _context
                .Clients.Where(c => c.BGPId == bgpClientId)
                .FirstOrDefaultAsync();

            if (client == null)
                throw new CoreException(CoreError.NotFound, "Client not found");

            var query = _context.Projects.Where(p =>
                p.ClientId == client.Id && p.BGPId != null && bgpProjectIds.Contains((int)p.BGPId)
            );

            foreach (var include in includes ?? [])
            {
                if (include is string includeString)
                {
                    query = query.Include(includeString);
                }
                else
                {
                    query = query.Include((Expression<Func<ProjectModel, object>>)include);
                }
            }

            return await query.ToListAsync();
        }

        public async Task<bool> EditProject(EditProjectRequest request)
        {
            var project = await _context
                .Projects.Where(p => p.Id == request.Id)
                .FirstOrDefaultAsync();

            if (project == null)
            {
                throw new Exception("project does not exist");
            }

            var original = project.Clone();

            if (request.Status != null)
            {
                project.Status = request.Status;
                project.UpdatedAt = DateTime.UtcNow;
            }

            if (request.CompletionPercentage != null)
            {
                if (request.CompletionPercentage < 0 || request.CompletionPercentage > 100)
                {
                    throw new Exception("completion percentage must be between 0 & 100");
                }

                project.CompletionPercentage = request.CompletionPercentage.Value;
                project.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            // Events: Status Changed.
            if (original.Status != project.Status)
            {
                await _eventService.LogEvent(
                    new EventPaylods.ProjectChanged
                    {
                        ClientId = project.ClientId,
                        ProjectId = project.Id,
                        HealthStatus = project.Status,
                    }
                );
            }

            return true;
        }

        public async Task<IEnumerable<ProjectModel>> GetProjectWithStages(
            Guid clientId,
            int projectId
        )
        {
            var projects = await _context
                .Projects.Where(p => p.BGPId == projectId && p.ClientId == clientId)
                .Include(p => p.Stages)
                .Include("Stages.ChildProjectStages")
                .ToListAsync();

            // Sort child stages by SortOrder for each project
            foreach (var project in projects)
            {
                foreach (var stage in project.Stages)
                {
                    if (stage.ChildProjectStages != null)
                    {
                        stage.ChildProjectStages = stage
                            .ChildProjectStages.OrderBy(cs => cs.SortOrder ?? short.MaxValue)
                            .ThenBy(cs => cs.CreatedAt)
                            .ToList();
                    }
                }
            }

            return projects;
        }

        public async Task<ProjectStageModel?> GetProjectStageById(Guid projectStageId)
        {
            var stage = await _context
                .ProjectStages.Where(p => p.Id == projectStageId)
                .Include(s => s.ChildProjectStages)
                .FirstAsync();

            // Sort child stages by SortOrder
            if (stage?.ChildProjectStages != null)
            {
                stage.ChildProjectStages = stage
                    .ChildProjectStages.OrderBy(cs => cs.SortOrder ?? short.MaxValue)
                    .ToList();
            }

            return stage;
        }

        public async Task UpdateProjectStages(ProjectModel request)
        {
            try
            {
                var project = await _context
                    .Projects.Include(p => p.Stages)
                    .ThenInclude(s => s.ChildProjectStages)
                    .Where(p => p.Id == request.Id)
                    .FirstAsync();

                if (project != null)
                {
                    // Sort child stages by SortOrder before processing
                    foreach (var stage in project.Stages)
                    {
                        if (stage.ChildProjectStages != null)
                        {
                            stage.ChildProjectStages = stage
                                .ChildProjectStages.OrderBy(cs => cs.SortOrder ?? short.MaxValue)
                                .ToList();
                        }
                    }

                    await deleteUnusedStages(project.Stages, request.Stages);

                    await updateProjectCompletion(project.Stages, request.Stages, project.Id);

                    await saveNewStages(project.Stages, request.Stages);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw e;
            }
        }

        private async Task deleteUnusedStages(
            List<ProjectStageModel> projectStages,
            List<ProjectStageModel> requestStages
        )
        {
            try
            {
                List<ProjectStageModel> _toDelete = flattenDeleteProjectStages(
                    projectStages,
                    requestStages
                );

                if (_toDelete.Count > 0)
                {
                    _context.ProjectStages.RemoveRange(_toDelete);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw e;
            }
        }

        private async Task<List<ProjectStageModel>> saveNewStages(
            List<ProjectStageModel> projectStages,
            List<ProjectStageModel> requestStages
        )
        {
            try
            {
                foreach (ProjectStageModel currentRequestStage in requestStages)
                {
                    ProjectStageModel? _match = projectStages.FirstOrDefault(s =>
                        s.Id == currentRequestStage.Id
                    );

                    if (_match == null) // Not in previous state so save it first
                    {
                        if (!_context.ProjectStages.Any(ps => ps.Id == currentRequestStage.Id))
                        {
                            currentRequestStage.Id = Guid.NewGuid();
                            await _context.ProjectStages.AddAsync(currentRequestStage);
                        }

                        projectStages.Add(currentRequestStage);
                    }
                    else
                    {
                        // Exists in current context, but might need an update?
                        _match.Kind = currentRequestStage.Kind;
                        _match.TranslationKey = currentRequestStage.TranslationKey;
                        _match.Status = currentRequestStage.Status;
                        _match.StartsAt = currentRequestStage.StartsAt;
                        _match.EndsAt = currentRequestStage.EndsAt;
                        _match.IsEnabled = currentRequestStage.IsEnabled;
                        _match.SortOrder = currentRequestStage.SortOrder;
                        _match.UpdatedAt = DateTime.UtcNow;
                        // _match.ProjectCompletion = currentRequestStage.ProjectCompletion; // keeping original value as this is a hidden field

                        if (currentRequestStage.ChildProjectStages.Count > 0)
                        {
                            _match.ChildProjectStages = await saveNewStages(
                                _match.ChildProjectStages,
                                currentRequestStage.ChildProjectStages
                            );
                        }
                    }

                    await _context.SaveChangesAsync();
                }

                return projectStages;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw e;
            }
        }

        private List<ProjectStageModel> flattenDeleteProjectStages(
            List<ProjectStageModel> projectStages,
            List<ProjectStageModel> requestStages
        )
        {
            List<ProjectStageModel> _toDelete = new List<ProjectStageModel>();

            foreach (ProjectStageModel stage in projectStages)
            {
                ProjectStageModel _match = requestStages.FirstOrDefault(s => s.Id == stage.Id);

                if (stage.ChildProjectStages.Count > 0)
                {
                    _toDelete.AddRange(
                        flattenDeleteProjectStages(
                            stage.ChildProjectStages,
                            _match != null
                                ? _match.ChildProjectStages
                                : new List<ProjectStageModel>()
                        )
                    );
                }

                if (_match == null)
                {
                    _toDelete.Add(stage);
                }
            }

            return _toDelete;
        }

        private async Task updateProjectCompletion(
            List<ProjectStageModel> projectStages,
            List<ProjectStageModel> requestStages,
            Guid projectId
        )
        {
            try
            {
                foreach (ProjectStageModel stage in requestStages)
                {
                    ProjectStageModel? previousStageStage = projectStages.FirstOrDefault(ps =>
                        ps.Id == stage.Id
                    );

                    if (previousStageStage != null)
                    {
                        // Update project completetion percentage if needed
                        if (
                            stage.Status == Data.Models.StageStatus.Complete
                            && previousStageStage.Status != Data.Models.StageStatus.Complete
                        )
                        {
                            ProjectModel project = await _context
                                .Projects.Include(p => p.Stages)
                                .ThenInclude(s => s.ChildProjectStages)
                                .Where(p => p.Id == projectId)
                                .FirstAsync();

                            if (project != null)
                            {
                                // Sort child stages by SortOrder
                                foreach (var projectStage in project.Stages)
                                {
                                    if (projectStage.ChildProjectStages != null)
                                    {
                                        projectStage.ChildProjectStages = projectStage
                                            .ChildProjectStages.OrderBy(cs =>
                                                cs.SortOrder ?? short.MaxValue
                                            )
                                            .ToList();
                                    }
                                }

                                var newCompletion = previousStageStage.ProjectCompletion ?? 0;

                                if (project.CompletionPercentage < newCompletion)
                                {
                                    project.CompletionPercentage = newCompletion;
                                    project.UpdatedAt = DateTime.UtcNow;
                                    await _context.SaveChangesAsync();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw e;
            }
        }

        public async Task CompleteProjectStage(Guid projectId, Guid projectStageId)
        {
            var project = await _context
                .Projects.Include(p => p.Stages)
                .ThenInclude(s => s.ChildProjectStages)
                .Where(p => p.Id == projectId)
                .FirstAsync();

            if (project != null)
            {
                // Sort child stages by SortOrder
                foreach (var projectStage in project.Stages)
                {
                    if (projectStage.ChildProjectStages != null)
                    {
                        projectStage.ChildProjectStages = projectStage
                            .ChildProjectStages.OrderBy(cs => cs.SortOrder ?? short.MaxValue)
                            .ToList();
                    }
                }

                var stage = project.Stages.FirstOrDefault(s => s.Id == projectStageId);

                if (stage != null)
                {
                    stage.Status = Data.Models.StageStatus.Complete;

                    if (stage.ProjectCompletion != null)
                    {
                        if (project.CompletionPercentage < stage.ProjectCompletion.Value)
                        {
                            project.CompletionPercentage = stage.ProjectCompletion.Value;
                            project.UpdatedAt = DateTime.UtcNow;
                        }
                    }

                    await _context.SaveChangesAsync();
                }
            }
        }

        public async Task<List<ProjectContact>> GetProjectContacts(Guid projectId)
        {
            List<ProjectContact> contacts = await _context
                .ProjectContacts.Where(pc => pc.ProjectId == projectId)
                .Include("User")
                .ToListAsync();
            return contacts;
        }

        //TODO Delete this before production
        public async Task GenerateMockProjectStages(ProjectStage[] stages)
        {
            // replace with whichever project id is in your db to generate mock data
            // and upate mock data file projectId fields to match that Id
            var project = await _context.Projects.FirstOrDefaultAsync();
            if (project != null)
            {
                foreach (var stage in stages)
                {
                    _context.ProjectStages.Add(stage);
                }

                project.Stages = stages.ToList();
                _context.SaveChanges();
            }
        }

        public async Task<ProjectTemplate?> GetTemplate(int bgpClientId, int bgpProjectId)
        {
            var project = await this.GetByBgpId(bgpClientId, bgpProjectId);
            if (project == null)
                return null;
            return await GetTemplate(project);
        }

        public async Task<ProjectTemplate?> GetTemplate(ProjectModel project)
        {
            // Await added to silence CS1998; GetProjectTemplate will become async later
            await Task.Yield();
            if (project.TemplateId == null)
                return null;
            Guid.TryParse(project.TemplateId, out var templateId);
            if (templateId == Guid.Empty)
                return null;
            var template = _templateService.GetProjectTemplate(templateId);
            return template;
        }
    }
}
