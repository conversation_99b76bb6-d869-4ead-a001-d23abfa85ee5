import { ChildType, IconName } from "@bcp/uikit";
import { IRecycleBinDocument } from "~/services/recycle-bin";

export interface DocumentRow {
  id: string;
  name: string;
  title: string;
  files?: number;
  fileType?: string;
  size?: number;
  modifiedAt: string;
  modifiedByName: string;
  createdAt: string;
  createdByName: string;
  originalLocation?: string;
  actionItemName?: string | null;
  user?: string;
  userAvatar?: string;
  selector?: string;
  actions?: string;
  url?: string;
  readonly?: boolean;
  folderOrFile: string;
  driveItemId: string;
  listItemId: number;
  restricted?: boolean;
  deletionInProgress?: boolean;
  isTemplated: boolean;
  requestSignature: string;
}

export interface TrashDocumentRow {
  id: string;
  title: string;
  files?: number;
  fileType?: string;
  modified?: string;
  originalLocation?: string;
  actionItemName?: string;
  timeLeft: string;
  user?: string;
  userAvatar?: string;
  selector?: string;
  actions?: string;
}

export enum DocumentTypeEnum {
  PDF = "document-pdf",
  DOCX = "document-docx",
  CSV = "document-csv",
  PPT = "document-ppt",
  PNG = "document-png",
  CODE = "document-code",
  ARCHIVE = "document-archive",
  EMAIL = "document-email",
  DATA = "document-data",
  VISIO = "document-visio",
  VIDEO = "document-video",
  DOCUMENT = "document-generic",
  UNKNOWN = "document-unknown",
}

export const iconNameMapping: Record<DocumentTypeEnum, IconName> = {
  [DocumentTypeEnum.PDF]: "document-pdf",
  [DocumentTypeEnum.DOCX]: "document-doc",
  [DocumentTypeEnum.CSV]: "document-csv",
  [DocumentTypeEnum.PPT]: "document-ppt",
  [DocumentTypeEnum.PNG]: "document-png",
  [DocumentTypeEnum.CODE]: "document-code",
  [DocumentTypeEnum.ARCHIVE]: "document-archive",
  [DocumentTypeEnum.EMAIL]: "document-email",
  [DocumentTypeEnum.DATA]: "document-data",
  [DocumentTypeEnum.VISIO]: "document-visio",
  [DocumentTypeEnum.VIDEO]: "document-video",
  [DocumentTypeEnum.DOCUMENT]: "documents-icon",
  [DocumentTypeEnum.UNKNOWN]: "documents-icon",
};

export interface DocumentItem {
  id: string;
  name: string;
  displayName: string;
  type: ChildType;
  createdAt: string;
  createdBy: string;
  modifiedAt: string;
  modifiedBy: string;
  restricted: boolean;
  readonly: boolean;
  size?: number;
  descendants?: DocumentItem[];
  listItemId?: number;
}

export type SortKey = "modified" | "title" | "actionItemName";

export type SortDirection = "asc" | "desc";

export interface SortConfig {
  key: SortKey;
  direction: SortDirection;
}

export const documentTemplate = {
  id: "",
  name: "",
  title: "",
  fileType: "",
  modifiedByName: "",
  modifiedAt: "",
  createdAt: "",
  createdByName: "",
  user: "",
  userAvatar: "",
  actionItemName: "",
  folderOrFile: "File",
  listItemId: 1,
  driveItemId: "",
  isTemplated: false,
  requestSignature: "None",
};

export const trashDocumentTemplate = {
  id: "",
  title: "",
  fileType: "",
  originalLocation: "",
  actionItemName: "",
  timeLeft: "",
  folderOrFile: "File",
};

export interface RecycleBinTableProps {
  files: IRecycleBinDocument[];
  isLoading: boolean;
}
