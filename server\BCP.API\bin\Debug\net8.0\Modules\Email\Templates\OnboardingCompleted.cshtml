@using BCP.Core.Email.Translations
@model BCP.Core.Email.Templates.OnboardingCompleted

@{
  Layout = "Common/Layout.cshtml";
  var T = Model.Translation;
}

<table role="presentation" cellpadding="0" cellspacing="0">
  <tr>
    <td style="padding-right:40px;">
      <h1 style="margin:0; color:#333; font-family:Arial, sans-serif; font-size:22px; line-height:32px; font-weight:400;">
        @T.OnboardingCompleted_Heading, @Model.User?.FirstName!
      </h1>
      <p style="margin:24px 0; color:#333; font-family:Arial, sans-serif; font-size:16px; line-height:24px;">
        @T.OnboardingCompleted_Body
      </p>
      <table role="presentation" cellpadding="0" cellspacing="0">
        <tr>
          <td style="background:#e81a3b; padding:8px 16px; border-radius:2px;">
            <a
              href="@Model.PortalUrl"
              target="_blank"
              style="color:#fff; text-decoration:none; font-family:<PERSON><PERSON>, sans-serif; font-size:16px; line-height:24px; font-weight:300;"
            >
              @T.OnboardingCompleted_Cta
            </a>
          </td>
        </tr>
      </table>
    </td>
    <td align="right" style="width:320px; text-align:right;">
      <img
        src="@($"{Model.AssetUrl}/overview-preview{(T is EnglishTranslation ? "" : "-fr")}.png")"
        alt="Welcome Image"
        width="320"
        style="width:320px; height:auto; border:0;"
      />
    </td>
  </tr>
  <tr>
    <td colspan="2" style="height:40px; border-bottom:1px solid #cccfd2;">
      &zwnj;
    </td>
  </tr>
</table>

@section Footer {
  <p style="margin:0 0 16px 0; color:#666; font-family:Arial, sans-serif; font-size:12px; line-height:18px;">
    @T.OnboardingCompleted_Footer_Note
  </p>
}
