import React from "react";
import styles from "./chip.module.css";
import { ChipProps, ChipType } from "./spec";
import { Icon } from "~/icon";
import { CustomAvatar } from "~/avatar";
import classNames from "classnames";
import { useTranslation } from "react-i18next";

export interface ChipPropsWithLeftElement extends ChipProps {
  leftElement?: React.ReactNode;
}

export const Chip: React.FC<ChipPropsWithLeftElement> = ({
  text,
  onDismissBtnClick,
  onClickAction,
  type,
  iconName,
  dataTestId = "uikit-chip",
  role,
  ariaLabel,
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  iconHeight = 16,
  iconWidth = 16,
  textOnly = false,
  id,
  leftElement,
}) => {
  const { t } = useTranslation("dropdown");
  const iconStyle = `chip--${type}`;
  const avatarStyle = `avatar--${type}`;
  const textStyle = `text--${type}`;
  const HTMLElement = onClickAction ? "button" : "span";

  return (
    <HTMLElement
      onClick={onClickAction}
      data-testid={dataTestId}
      role={role}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
      className={styles.buttonReset}
      id={id}
    >
      <div
        className={classNames(styles.chip, {
          [styles[iconStyle]]: type && type !== ChipType.DEFAULT,
          [styles.chip]: !type || type === ChipType.DEFAULT,
          [styles.textOnly]: textOnly,
          [styles.interactive]: onClickAction,
        })}
      >
        {!textOnly && (
          <div
            className={
              type && type !== ChipType.DEFAULT
                ? styles[avatarStyle]
                : styles.avatar
            }
          >
            {leftElement ? (
              leftElement
            ) : (type === ChipType.DEFAULT || !type) && !iconName && text ? (
              <CustomAvatar
                initials={text.slice(0, 1)}
                avatarSize={"chip"}
                fontSize="x-small"
                type="monogram"
              />
            ) : (
              type !== ChipType.STATUS && (
                <Icon
                  iconName={iconName!}
                  altText={iconName!}
                  width={iconWidth}
                  height={iconHeight}
                  color={type == ChipType.ERROR ? "#98002E" : undefined}
                ></Icon>
              )
            )}
          </div>
        )}
        <div
          className={
            type && type != ChipType.DEFAULT ? styles[textStyle] : styles.text
          }
        >
          {text}
        </div>
        {onDismissBtnClick && (
          <button
            className={styles.dismissButton}
            onClick={onDismissBtnClick}
            aria-label={t("remove", { option: text })}
          >
            <Icon iconName="dismiss-icon" />
          </button>
        )}
      </div>
    </HTMLElement>
  );
};
