import React from "react";
import styles from "./checkboxDropdownItem.module.css";
import { Checkbox } from "~/checkbox";
import { ChipDropdownItem } from "../../spec";
import { getInitials } from "~/utils/getInitials";
import { CustomAvatar } from "~/avatar";

export const CheckboxDropdownItem: React.FC<ChipDropdownItem> = ({
  id,
  label,
  value,
  onClick,
  includeAvatar,
  checked,
  avatarUrl,
}) => {
  const handleValueChange = () => {
    onClick?.(value);
  };

  return (
    <div className={styles.checkboxDropdownItem}>
      <Checkbox
        onChange={handleValueChange}
        id={`${id}-${value}`}
        value={checked}
        customLabel={
          <div className={styles.avatarAndLabel}>
            {includeAvatar && (
              <div className={styles.avatarWrapper}>
                <CustomAvatar
                  initials={getInitials(label)}
                  imageUrl={avatarUrl ?? ""}
                  avatarSize="x-small"
                  fontSize="x-small"
                  type={avatarUrl ? "photo" : "monogram"}
                />
              </div>
            )}
            <span className={styles.label}>{label}</span>
          </div>
        }
      />
    </div>
  );
};
