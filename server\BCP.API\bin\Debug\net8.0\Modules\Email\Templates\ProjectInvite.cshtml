@model BCP.Core.Email.Templates.ProjectInvite

@{
  Layout = "Common/Layout.cshtml";
  var T = Model.Translation;
}

<table role="presentation" cellpadding="0" cellspacing="0">
  <tr>
    <td style="padding-bottom:16px; border-bottom:1px solid #dcdcdc;">
      <p style="margin:0 0 16px 0; color:#333; font-size:16px; line-height:24px;">
        @Model.Body
      </p>
      <ul style="margin:16px 24px; padding:0;" type="disc">
        @foreach (var bullet in T.ProjectInvite_Bullets) {
          <li style="margin:0 0 4px 0; color:#333; font-size:16px; line-height:24px;">
            @bullet
          </li>
        }
      </ul>
    </td>
  </tr>
  <tr>
    <td style="padding:16px 0;">
      <h1 style="margin:0; color:#333; font-size:22px; line-height:32px; font-weight:400;">
        @Model.Project?.Name
      </h1>
    </td>
  </tr>
  <tr>
    <td>
      <table role="presentation" cellpadding="0" cellspacing="0">
        <tr>
          <td style="background:#e81a3b; padding:8px 16px; border-radius:2px;">
            <a
              href="@Model.PortalUrl/client/@Model.Client?.Id/project/@Model.Project?.Id"
              target="_blank"
              style="color:#fff; text-decoration:none; font-size:16px; line-height:24px; font-weight:300;"
            >
              @T.ProjectInvite_Cta &rarr;
            </a>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>

@section Footer {
  <p style="font-size:12px; line-height:18px; color:#666; margin:0 0 16px 0;">
    @T.Notification_Footer_Note <a href="@Model.PortalUrl/settings/notifications">@T.Notification_Footer_Cta</a>.
  </p>
}
