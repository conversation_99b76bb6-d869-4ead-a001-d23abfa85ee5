using BCP.Core.BGP.ControlAPI.Models;
using BCP.Core.Template.Models;
using BCP.Data.Models;
using ProjectModel = BCP.Data.Models.Project;
using ProjectStageModel = BCP.Data.Models.ProjectStage;

namespace BCP.Core.Project
{
    public interface IProjectService
    {
        Task<IEnumerable<ProjectModel>> GetUserProjects(Guid clientId);
        Task<ProjectModel?> Get(Guid id, params object[] includes);
        Task<IEnumerable<ProjectModel>> Get(Guid[] ids, params object[] includes);
        Task<ProjectModel?> GetByBgpId(int bgpClientId, int bgpProjectId, params object[] includes);
        Task<IEnumerable<ProjectModel>> GetByBgpId(
            int bgpClientId,
            int[] bgpProjectIds,
            params object[] includes
        );

        Task<bool> EditProject(EditProjectRequest request);

        Task<IEnumerable<BDOClientProjectResponse>> GetUserAccessForProjectsAsync(
            GetUserAccessForProjectsRequest request
        );

        Task<BCP.Core.BGP.FirmAPI.Project?> GetBgpProject(int bgpClientId, int bgpProjectId);

        Task<bool> AddOrRemoveUserAsync(EditUserRequest request);

        Task<ProjectStageModel?> GetProjectStageById(Guid projectStageId);

        Task UpdateProjectStages(ProjectModel project);

        Task CompleteProjectStage(Guid projectId, Guid projectStageId);

        Task GenerateMockProjectStages(ProjectStageModel[] stages);

        Task<IEnumerable<ProjectModel>> GetProjectWithStages(Guid clientId, int projectId);

        Task<List<ProjectContact>> GetProjectContacts(Guid projectId);
        Task<ProjectTemplate?> GetTemplate(int bgpClientId, int bgpProjectId);
        Task<ProjectTemplate?> GetTemplate(ProjectModel project);
    }
}
