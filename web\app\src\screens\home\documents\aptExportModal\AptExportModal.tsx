import { Button, ButtonSizeEnum, ButtonTypeEnum, Icon, Modal, ModalSize } from "@bcp/uikit";
import { DocumentRow } from "../spec";
import { ValidatedDocument, validateDocumentForAptExport } from "../utils";
import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import styles from "./aptExportModal.module.css";

interface AptExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (documents: DocumentRow[]) => void;
  selectedDocuments: DocumentRow[];
  isExporting?: boolean;
}

export const AptExportModal = ({
  isOpen,
  onClose,
  onExport,
  selectedDocuments,
  isExporting = false,
}: AptExportModalProps) => {
  const { t } = useTranslation();
  const [validatedDocuments, setValidatedDocuments] = useState<ValidatedDocument[]>([]);
  const [isScrollable, setIsScrollable] = useState(false);
  const documentsListRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && selectedDocuments.length > 0) {
      const validated = selectedDocuments.map(doc => validateDocumentForAptExport(doc));
      setValidatedDocuments(validated);
    }
  }, [isOpen, selectedDocuments]);

  useEffect(() => {
    const checkScrollable = () => {
      if (documentsListRef.current) {
        const { scrollHeight, clientHeight } = documentsListRef.current;
        setIsScrollable(scrollHeight > clientHeight);
      }
    };

    if (isOpen && validatedDocuments.length > 0) {
      // Check after a small delay to ensure DOM is updated
      setTimeout(checkScrollable, 100);
    }
  }, [isOpen, validatedDocuments]);

  const handleRemoveDocument = (documentId: string) => {
    setValidatedDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };

  const handleExport = () => {
    const documentsToExport = validatedDocuments.map(({ errors, isValid, ...doc }) => doc);
    onExport(documentsToExport);
  };

  const hasErrors = validatedDocuments.some(doc => !doc.isValid);
  const canExport = validatedDocuments.length > 0 && !hasErrors && !isExporting;

  const getFileIcon = (document: ValidatedDocument) => {
    if (document.folderOrFile.toLowerCase() === 'folder') {
      return 'folder-icon';
    }
    return 'action-item-icon';
  };

  const getFileTypeDisplay = (document: ValidatedDocument) => {
    if (document.folderOrFile.toLowerCase() === 'folder') {
      const fileCount = document.files || 0;
      return fileCount === 1 ? `${fileCount} file` : `${fileCount} files`;
    }
    return document.fileType?.toUpperCase() || 'UNKNOWN';
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getErrorIcon = (errorType: string) => {
    switch (errorType) {
      case 'invalid_file_type':
        return 'alert-circle';
      case 'file_size_exceeded':
        return 'alert-triangle';
      default:
        return 'alert-circle';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={ModalSize.MEDIUM}
      title="Confirm Export to APT Workspace"
      footerButtonAlignment="right"
      footerButtons={[
        <Button
          key="cancel"
          id="cancel-button"
          type={ButtonTypeEnum.secondary}
          size={ButtonSizeEnum.large}
          onClick={onClose}
          disabled={isExporting}
          label="Cancel"
        />,
        <Button
          key="export"
          id="export-button"
          type={ButtonTypeEnum.primary}
          size={ButtonSizeEnum.large}
          onClick={handleExport}
          disabled={!canExport}
          loading={isExporting}
          label="Export to APT"
        />,
      ]}
    >
      <div className={styles.modalContent}>
        <div className={styles.description}>
          <p>You are about to export the following files and folders to APT:</p>
          <ul>
            <li>Folders will retain the same structure they have in this portal.</li>
            <li>Subfolders and all included files will be exported as part of each selected folder.</li>
          </ul>
        </div>

        <div
          ref={documentsListRef}
          className={`${styles.documentsList} ${isScrollable ? styles.scrollable : ''}`}
        >
          {validatedDocuments.map((document) => (
            <div
              key={document.id}
              className={`${styles.documentItem} ${!document.isValid ? styles.hasError : ''}`}
            >
              <div className={styles.documentInfo}>
                <Icon iconName={getFileIcon(document)} size={16} />
                <div className={styles.documentDetails}>
                  <span className={styles.documentName} title={document.title}>
                    {document.title}
                  </span>
                  <span className={styles.documentMeta}>
                    {getFileTypeDisplay(document)}
                    {document.size && document.folderOrFile.toLowerCase() !== 'folder' && (
                      <> • {formatFileSize(document.size)}</>
                    )}
                  </span>
                </div>
              </div>

              {document.errors.length > 0 && (
                <div className={styles.errorMessages}>
                  {document.errors.map((error, index) => (
                    <div key={index} className={styles.errorMessage}>
                      <Icon iconName={getErrorIcon(error.type)} size={14} />
                      <span>{error.message}</span>
                    </div>
                  ))}
                </div>
              )}

              <button
                className={styles.removeButton}
                onClick={() => handleRemoveDocument(document.id)}
                title="Remove from export"
              >
                <Icon name="x" size={16} />
              </button>
            </div>
          ))}
        </div>

        {validatedDocuments.length === 0 && (
          <div className={styles.emptyState}>
            <p>No files or folders selected for export.</p>
          </div>
        )}
      </div>
    </Modal>
  );
};
