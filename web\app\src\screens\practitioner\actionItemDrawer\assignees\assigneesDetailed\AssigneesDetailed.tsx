import classNames from "classnames";
import styles from "./assigneesDetailed.module.css";
import {
  ActionItemAssignee,
  BGPSigningStatusOutcomeEnum,
} from "~/services/action-items/spec";
import { CustomAvatar, getInitials, Icon } from "@bcp/uikit";
import { useTranslation } from "react-i18next";

interface Props {
  assignees: ActionItemAssignee[];
}

export const AssigneesDetailed: React.FC<Props> = ({ assignees }) => {
  const sortedAssignees = [...assignees].sort((a, b) => {
    if (a.routingOrder === null && b.routingOrder === null) return 0;
    if (a.routingOrder === null) return 1;
    if (b.routingOrder === null) return -1;
    return a.routingOrder - b.routingOrder;
  });

  return (
    <div className={styles.container}>
      {sortedAssignees.map(assignee => (
        <Assignee assignee={assignee} key={assignee.user.bgpId} />
      ))}
    </div>
  );
};

const Assignee: React.FC<{ assignee: ActionItemAssignee }> = ({ assignee }) => {
  const { t } = useTranslation("actionItems");

  const isSigned = assignee.outcome === BGPSigningStatusOutcomeEnum.Signed;
  const isDeclined = assignee.outcome === BGPSigningStatusOutcomeEnum.Declined;

  return (
    <div
      className={classNames(
        styles.assignee,
        isSigned && styles.assigneeSigned,
        isDeclined && styles.assigneeDeclined
      )}
    >
      {assignee.routingOrder && (
        <span className={styles.order}>{assignee.routingOrder}</span>
      )}

      <div className={styles.user}>
        <CustomAvatar
          avatarSize="x-small"
          fontSize="x-small"
          type="monogram"
          initials={getInitials(assignee.user.displayName)}
        />
        {assignee.user.displayName}
      </div>

      {isSigned && (
        <Icon
          iconName="checkmark-circle-filled"
          color="#218F8B"
          altText={t("signed")}
        />
      )}

      {isDeclined && (
        <Icon
          iconName="dismiss-circle-filled"
          color="#D67900"
          altText={t("declined")}
        />
      )}
    </div>
  );
};
