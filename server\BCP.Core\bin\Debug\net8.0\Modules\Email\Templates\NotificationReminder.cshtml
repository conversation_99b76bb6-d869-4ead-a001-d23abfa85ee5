@using BCP.Core.Email.Translations
@model BCP.Core.Email.Templates.NotificationReminder

@{
  Layout = "Common/Layout.cshtml";
  var T = Model.Translation;
}

<table role="presentation" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <h2 style="margin:0; color:#666; font-size:16px; font-style:normal; font-weight:400; line-height:24px;">
        @Model.DateRange
      </h2>
    </td>
  </tr>
  <tr>
    <td style="padding: 16px 0;">
      <h1 style="margin:0; color:#333; font-size:22px; font-style:normal; font-weight:400; line-height:24px;">
        @Model.Title
      </h1>
    </td>
  </tr>
  <tr>
    <td>
      <table role="presentation" cellpadding="0" cellspacing="0">
        <tr>
          <td style="background:#e81a3b; padding:8px 16px; border-radius:2px;">
            <a
              href="@Model.PortalUrl/notifications"
              target="_blank"
              style="color:#fff; text-decoration:none; font-size:16px; line-height:24px; font-weight:300;"
            >
              @T.NotificationReminder_Cta &rarr;
            </a>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="padding-top: 40px">
      <!--[if mso]>&zwnj;<![endif]-->
      <img
        src="@($"{Model.AssetUrl}/notification-reminder{(T is EnglishTranslation ? "" : "-fr")}.png")"
        alt="Notification Reminders"
        width="526"
        style="width:526px; height:auto; border:0;"
      />
    </td>
  </tr>
</table>

@section Footer {
  <p style="font-size:12px; line-height:18px; color:#666; margin:0 0 16px 0;">
    @T.Notification_Footer_Note <a href="@Model.PortalUrl/settings/notifications">@T.Notification_Footer_Cta</a>.
  </p>
  <p style="font-size:12px; line-height:18px; color:#666; margin:16px 0;">
    @T.NotificationReminder_Unsubscribe <a href="@Model.PortalUrl/settings/notifications">@T.Notification_Footer_Short_Cta</a>.
  </p>
}
