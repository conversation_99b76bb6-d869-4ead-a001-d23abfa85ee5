[{"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "DeleteActionItem", "RelativePath": "api/actionitem", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDODeleteActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetActionItem", "RelativePath": "api/actionitem/actionitem", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberFirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "actionItemId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetActionItems", "RelativePath": "api/actionitem/actionitems", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberFirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "AddCommentToActionItem", "RelativePath": "api/actionitem/addcomment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOAddCommentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetComments", "RelativePath": "api/actionitem/comments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.String", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CompleteActionItem", "RelativePath": "api/actionitem/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOCompleteActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CompleteSigningRequest", "RelativePath": "api/actionitem/completesigningrequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "actionItemId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CopyDocumentToTask", "RelativePath": "api/actionitem/copydocumenttotask", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sourceTaskId", "Type": "System.Guid", "IsRequired": false}, {"Name": "sourceTaskGroupId", "Type": "System.Guid", "IsRequired": false}, {"Name": "sourceFileName", "Type": "System.String", "IsRequired": false}, {"Name": "targetTaskId", "Type": "System.Guid", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "targetPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CreateDocumentRequest", "RelativePath": "api/actionitem/createdocumentrequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.ActionItem.Models.BCPCreateDocumentActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CreateSignatureRequest", "RelativePath": "api/actionitem/createsignaturerequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "IsPriority", "Type": "System.Boolean", "IsRequired": false}, {"Name": "SigningDocument", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "EnvelopeId", "Type": "System.String", "IsRequired": false}, {"Name": "EnableSigningOrder", "Type": "System.Boolean", "IsRequired": false}, {"Name": "actionItemDetails", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "DeleteComment", "RelativePath": "api/actionitem/deletecomment", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDODeleteCommentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetActionItem", "RelativePath": "api/actionitem/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Guid", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetDocumentDirectory", "RelativePath": "api/actionitem/documentdirectory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Guid", "IsRequired": false}, {"Name": "taskGroupId", "Type": "System.Guid", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetActionItemDocuments", "RelativePath": "api/actionitem/documents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "actionItemId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CreateDraftEnvelope", "RelativePath": "api/actionitem/docusign/envelope", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "signers", "Type": "System.String", "IsRequired": false}, {"Name": "enableSigningOrder", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "EditEnvelope", "RelativePath": "api/actionitem/docusign/envelope/edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.ActionItem.Models.EditEnvelopeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "UpdateEnvelopeSigners", "RelativePath": "api/actionitem/docusign/envelope/signers", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.ActionItem.Models.UpdateEnvelopeSignersRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "MarkActionItemAsPriority", "RelativePath": "api/actionitem/priority", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "ReturnActionItem", "RelativePath": "api/actionitem/return", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOReturnActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "SignActionItem", "RelativePath": "api/actionitem/sign", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOSignActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "Submit", "RelativePath": "api/actionitem/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.SubmitActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "CreateTaskGroup", "RelativePath": "api/actionitem/taskgroup/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "groupName", "Type": "System.String", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "GetTaskGroups", "RelativePath": "api/actionitem/taskgroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "UnMarkActionItemAsPriority", "RelativePath": "api/actionitem/unmarkpriority", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOActionItemPriorityRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "UpdateActionItem", "RelativePath": "api/actionitem/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "body", "Type": "BCP.Core.ActionItem.Models.BCPUpdateActionItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "UpdateComment", "RelativePath": "api/actionitem/updatecomment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOUpdateCommentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ActionItemController", "Method": "UploadDocuments", "RelativePath": "api/actionitem/uploaddocuments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "actionItemId", "Type": "System.String", "IsRequired": false}, {"Name": "path", "Type": "System.String", "IsRequired": false}, {"Name": "files", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.AuthController", "Method": "Exchange", "RelativePath": "api/auth/exchange", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/auth/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.AuthController", "Method": "Status", "RelativePath": "api/auth/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.Auth.AuthStatus", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ClientController", "Method": "Get", "RelativePath": "api/client", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeUserCount", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeProjectCount", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BCP.Core.Modules.Client.Response.ClientListResponse, BCP.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ClientController", "Method": "GetContacts", "RelativePath": "api/client/{id}/contacts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BCP.Core.BGP.ControlAPI.Models.BDOClientContact, BCP.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ClientController", "Method": "Search", "RelativePath": "api/client/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchQuery", "Type": "System.String", "IsRequired": false}, {"Name": "clientId", "Type": "System.String", "IsRequired": false}, {"Name": "memberFirmId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.BGP.ControlAPI.Models.BDOUser[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ContextController", "Method": "Get", "RelativePath": "api/context", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Data.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "CreateNewFolder", "RelativePath": "api/document/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.FolderCreationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "DeleteRecycleBinItemsPermanently", "RelativePath": "api/document/deleterecyclebinitemspermanently", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDODeleteRecycleBinItemPermanentlyRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "CreateDownloadFileAsync", "RelativePath": "api/document/downloadfile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.Document.Models.DocumentDownloadRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "DownloadFileAsync", "RelativePath": "api/document/downloadfile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "downloadKey", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "GetFolderContentAsync", "RelativePath": "api/document/foldercontent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Document.Models.DocumentsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "GetAvailableUsersToAssignAccess", "RelativePath": "api/document/getavailableuserstoassignaccess", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "MemberFirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "ClientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "ProjectId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ItemId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "NewFolderPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "GetDocumentDetailsByDriveItemId", "RelativePath": "api/document/getdocumentdetailsbydriveitemid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "driveItemId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Document.Models.DocumentResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "GetDocumentDetailsByPath", "RelativePath": "api/document/getdocumentdetailsbypath", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "path", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Document.Models.DocumentResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "GetPermissions", "RelativePath": "api/document/getpermissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "memberFirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "ManageAccess", "RelativePath": "api/document/manageaccess", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOManageAccessRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "MoveDocuments", "RelativePath": "api/document/move", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "body", "Type": "BCP.Core.BGP.ControlAPI.Models.MoveDocumentsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "MoveDocumentsToRecycleBin", "RelativePath": "api/document/movedocumentstorecyclebin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOMoveToTrashRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "GetRecycleBin", "RelativePath": "api/document/recyclebin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.BGP.ControlAPI.Models.BDORecycleBinDocument[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "RenameDocument", "RelativePath": "api/document/rename", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDORenameDocumentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "RestoreDocuments", "RelativePath": "api/document/restoredocuments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDORestoreRequest", "IsRequired": true}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "UpdatePermissions", "RelativePath": "api/document/updatepermissions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOUpdateFolderPermissionsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.DocumentController", "Method": "UploadDocument", "RelativePath": "api/document/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "path", "Type": "System.String", "IsRequired": false}, {"Name": "folderListItemId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.EmailController", "Method": "GetAsset", "RelativePath": "api/email/assets/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.EmailController", "Method": "PreviewEmail", "RelativePath": "api/email/preview/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.EventsController", "Method": "QueryEvents", "RelativePath": "api/events", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BatchIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "BatchSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "EventNames", "Type": "System.Collections.Generic.List`1[[BCP.Data.Models.EventName, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "From", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "To", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserIds", "Type": "System.Collections.Generic.List`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Common.BatchResult`1[[BCP.Data.Models.Event, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.EventsController", "Method": "GetEventById", "RelativePath": "api/events/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.Event", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.EventsController", "Method": "DangerPurgeEvents", "RelativePath": "api/events/danger-purge-events", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "confirmation", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.EventsController", "Method": "QueryEventLogs", "RelativePath": "api/events/logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BatchIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "BatchSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "EventIds", "Type": "System.Collections.Generic.List`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Handlers", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Statuses", "Type": "System.Collections.Generic.List`1[[BCP.Data.Models.EventStatus, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "MinRunNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Common.BatchResult`1[[BCP.Data.Models.EventLog, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.EventsController", "Method": "GetEventLogById", "RelativePath": "api/events/logs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.EventLog", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.HealthController", "Method": "Get", "RelativePath": "api/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.JobsController", "Method": "Query", "RelativePath": "api/jobs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BatchIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "BatchSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "JobNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Statuses", "Type": "System.Collections.Generic.List`1[[BCP.Data.Models.JobStatus, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ScheduledOnly", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Common.BatchResult`1[[BCP.Data.Models.JobLog, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.JobsController", "Method": "GetById", "RelativePath": "api/jobs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.JobLog", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.LocksController", "Method": "Get", "RelativePath": "api/locks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.LocksController", "Method": "Purge", "RelativePath": "api/locks/purge", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "older<PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.LocksController", "Method": "Test", "RelativePath": "api/locks/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.NotificationController", "Method": "Get", "RelativePath": "api/notification", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "BatchIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "BatchSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "ProjectIds", "Type": "System.Guid[]", "IsRequired": false}, {"Name": "NotificationTypes", "Type": "System.String[]", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "ImportantOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "UnreadOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "NotificationTypesEnum", "Type": "BCP.Data.Models.NotificationType[]", "IsRequired": false}, {"Name": "SortDirectionEnum", "Type": "System.String", "IsRequired": false}, {"Name": "SortByEnum", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.Common.BatchResult`1[[BCP.Core.Notifications.NotificationResponse, BCP.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.NotificationController", "Method": "GetNotificationCount", "RelativePath": "api/notification/getnotificationcount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.Notifications.NotificationCountResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.NotificationController", "Method": "GetProjects", "RelativePath": "api/notification/projects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BCP.Core.Notifications.GenericEntity, BCP.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.NotificationController", "Method": "UpdateImportance", "RelativePath": "api/notification/updateimportance", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.Notifications.UpdateImportanceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.NotificationController", "Method": "UpdateReadStatus", "RelativePath": "api/notification/updatereadstatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.Notifications.UpdateReadStatusRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "AcceptCookies", "RelativePath": "api/onboarding/acceptcookies", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.Modules.Onboarding.Models.AcceptCookieRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "AcceptPrivacyPolicy", "RelativePath": "api/onboarding/acceptprivacypolicy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "AcceptTerms", "RelativePath": "api/onboarding/acceptterms", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "CompleteOnboardingProcess", "RelativePath": "api/onboarding/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "GetPrivacyPolicyAcceptance", "RelativePath": "api/onboarding/privacypolicyacceptance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.Modules.Onboarding.Models.PrivacyPolicyAcceptanceResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "GetOnboardingProgress", "RelativePath": "api/onboarding/progress", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.User.OnboardingProgress", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.OnboardingController", "Method": "GetTermsAcceptance", "RelativePath": "api/onboarding/termsacceptance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.Modules.Onboarding.Models.TermsAcceptanceResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "Get", "RelativePath": "api/project", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BCP.Data.Models.Project, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetAptLinkingStatus", "RelativePath": "api/project/apt-linking-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetClientProjects", "RelativePath": "api/project/client-projects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberFirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "CompleteProjectStage", "RelativePath": "api/project/completeprojectstage", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Guid", "IsRequired": false}, {"Name": "projectStageId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "EditUser", "RelativePath": "api/project/edit-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.Project.EditUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "EditProject", "RelativePath": "api/project/editproject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "editProjectRequest", "Type": "BCP.Core.Project.EditProjectRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetUserAccessForProjects", "RelativePath": "api/project/get-user-access", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "ClientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "IsBdoGroup", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ProjectIds", "Type": "System.Int32[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetProjectStageById", "RelativePath": "api/project/getprojectstagebyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectStageId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetProjectWithStages", "RelativePath": "api/project/getprojectwithstages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Guid", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetProjectMembers", "RelativePath": "api/project/members", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberFirmId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "GetProjectContacts", "RelativePath": "api/project/projectcontacts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "RenameProject", "RelativePath": "api/project/rename", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}, {"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.ProjectController", "Method": "UpdateProjectStage", "RelativePath": "api/project/updateprojectstages", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "project", "Type": "BCP.Data.Models.Project", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.RiskController", "Method": "GetProjectRisks", "RelativePath": "api/risk", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BCP.Data.Models.ProjectRisk, BCP.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.RiskController", "Method": "CreateProjectRisk", "RelativePath": "api/risk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOCreateProjectRiskRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.RiskController", "Method": "DeleteProjectRisk", "RelativePath": "api/risk", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectRiskId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.RiskController", "Method": "GetRisksLibrary", "RelativePath": "api/risk/library", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BCP.Core.Risks.Models.Risk, BCP.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.SettingController", "Method": "Get", "RelativePath": "api/setting", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.Common.WebSettings", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.SyncController", "Method": "Index", "RelativePath": "api/sync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "AddUsersToProject", "RelativePath": "api/teammember/addusers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOAddUsersRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Core.User.AddUsersResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "GetMemberFirmUsers", "RelativePath": "api/teammember/getmemberfirmusers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.BGP.ControlAPI.Models.BDOSearchUser[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "GetPortalUsers", "RelativePath": "api/teammember/getportalusers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Core.BGP.ControlAPI.Models.BDOPortalMember[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "GetTeamMembers", "RelativePath": "api/teammember/projectteammembers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "RemoveUserFromProject", "RelativePath": "api/teammember/removeuser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.User.RemoveBgpAccessRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "ResendEmailInvitationAsync", "RelativePath": "api/teammember/resendinvite", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.ControlAPI.Models.BDOReinviteUser", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "UpdateTeamMemberProfile", "RelativePath": "api/teammember/updateuser", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userInfo", "Type": "BCP.Data.Models.User", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.TeamMemberController", "Method": "UpdateRole", "RelativePath": "api/teammember/updateuserrole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.User.UpdateAccessRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BCP.API.Controllers.TemplateController", "Method": "GetProjectTemplate", "RelativePath": "api/template/project/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Core.Template.Models.ProjectTemplate", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.TemplateController", "Method": "GetProjectTemplates", "RelativePath": "api/template/projects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BCP.Core.Template.Models.ProjectTemplate, BCP.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.UserController", "Method": "GetUser", "RelativePath": "api/user/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.UserController", "Method": "Update", "RelativePath": "api/user/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "BCP.Core.User.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.UserController", "Method": "GetUser", "RelativePath": "api/user/byemail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BCP.Data.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.UserController", "Method": "GetCurrentUserRole", "RelativePath": "api/user/getcurrentuserrole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "projectId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.UserController", "Method": "GetMe", "RelativePath": "api/user/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Data.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.WebhooksController", "Method": "GetBgpClient", "RelativePath": "api/webhooks/client/{bgpClientId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "bgpClientId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Core.BGP.FirmAPI.Client", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.WebhooksController", "Method": "GetBgpProject", "RelativePath": "api/webhooks/client/{bgpClientId}/project/{bgpProjectId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "bgpClientId", "Type": "System.String", "IsRequired": true}, {"Name": "bgpProjectId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Core.BGP.FirmAPI.Project", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.WebhooksController", "Method": "GetClientByExternalReferenceId", "RelativePath": "api/webhooks/crm/client/{externalReferenceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalReferenceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.Client", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.WebhooksController", "Method": "CreateClientAndProjects", "RelativePath": "api/webhooks/crm/createclientandprojects", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BCP.Core.BGP.FirmAPI.PlatformIntegrationCreateClientAndProjectRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.WebhooksController", "Method": "GetProjectByExternalId", "RelativePath": "api/webhooks/crm/project/{externalReferenceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalReferenceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BCP.Data.Models.Project", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BCP.API.Controllers.ContextController", "Method": "GetBGPContext", "RelativePath": "context", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BCP.Core.BGP.ControlAPI.Models.BDOUser", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]