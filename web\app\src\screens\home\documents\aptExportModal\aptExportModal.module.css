.modalContent {
  padding: 24px;
}

.description {
  margin-bottom: 24px;
}

.description p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.description ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.description li {
  margin-bottom: 4px;
}

.documentsList {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  position: relative;
}

.documentsList.scrollable {
  box-shadow: inset 0 -10px 10px -10px rgba(0, 0, 0, 0.1);
}

.documentItem {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
  position: relative;
}

.documentItem:last-child {
  border-bottom: none;
}

.documentItem.hasError {
  border-left: 4px solid #e81a3b;
  background: #fef7f7;
}

.documentInfo {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.documentDetails {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

.documentName {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  cursor: help;
}

.documentMeta {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.errorMessages {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
  margin-left: 28px;
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e81a3b;
  font-size: 12px;
  line-height: 1.4;
}

.errorMessage svg {
  flex-shrink: 0;
  color: #e81a3b;
}

.removeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: 12px;
}

.removeButton:hover {
  background: #f0f0f0;
  color: #e81a3b;
}

.removeButton:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.emptyState {
  padding: 40px 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.emptyState p {
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modalContent {
    padding: 16px;
  }
  
  .documentItem {
    padding: 12px;
    flex-direction: column;
    align-items: stretch;
  }
  
  .removeButton {
    align-self: flex-end;
    margin-left: 0;
    margin-top: 8px;
  }
  
  .documentInfo {
    margin-bottom: 8px;
  }
  
  .errorMessages {
    margin-left: 0;
    margin-top: 8px;
  }
}

/* Scrollbar styling */
.documentsList::-webkit-scrollbar {
  width: 6px;
}

.documentsList::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.documentsList::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.documentsList::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
