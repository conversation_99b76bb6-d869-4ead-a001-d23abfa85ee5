import {
  CustomAvatar,
  AssigneeTooltip,
  Icon,
  getDueDateLabel,
  getInitials,
  TaskType,
  StatusBadge,
  StatusBadgeType,
  ActionItemStatusType,
} from "@bcp/uikit";
import styles from "./actionItemEntry.module.css";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";
import {
  ActionItemAssignee,
  ActionItemType,
} from "~/services/action-items/spec";
import { useRoleService } from "~/services/role";

interface ActionItemEntryProps extends defaultProps {
  label: string;
  dueDate: string | Date;
  name?: string;
  avatarColor?: string;
  assignees: ActionItemAssignee[];
  onClick: () => void;
  drawerActionItemId?: string;
  id?: string;
  actionItemType: string;
  flagged?: boolean;
  status: string;
}

export const ActionItemEntry: React.FC<ActionItemEntryProps> = ({
  label,
  dueDate,
  assignees,
  onClick,
  dataTestId = "action-item-entry",
  id,
  drawerActionItemId,
  actionItemType,
  flagged,
  status,
}) => {
  const { i18n } = useTranslation("global");
  const { t: tActionItems } = useTranslation("actionItems");
  const { isPractitioner } = useRoleService();

  const taskIcon = {
    iconName:
      actionItemType === ActionItemType.RequestItem
        ? "up-arrow"
        : "signature-request",
    backgroundColor:
      actionItemType === ActionItemType.RequestItem ? "ocean" : "jade",
    width: actionItemType === ActionItemType.RequestItem ? 10 : 14,
    height: actionItemType === ActionItemType.RequestItem ? 12 : 14,
  };

  const dueLabel = dueDate
    ? getDueDateLabel(dueDate as string, i18n.language)
    : "";

  return (
    <button
      className={styles.actionItemEntry}
      onClick={onClick}
      data-testid={dataTestId}
      aria-label={tActionItems("open-action-item-details", { label })}
      aria-haspopup="dialog"
      aria-expanded={id === drawerActionItemId}
      aria-controls={`${id}-drawer-panel`}
    >
      <div className={styles.label} aria-label={label}>
        <TaskType
          iconName={taskIcon.iconName}
          backgroundColor={taskIcon.backgroundColor}
          width={taskIcon.width}
          height={taskIcon.height}
        />
        {label}
        {flagged && (
          <Icon iconName="priority" color="red" width={12} height={11.5} />
        )}
      </div>
      <div className={styles.endElements}>
        <div className={styles.user}>
          {assignees.length >= 1 && (
            <div className={styles.assigneeCell}>
              {assignees.slice(0, 3).map((assignee, index) => (
                <span
                  key={index}
                  className={styles.assignee}
                  style={{ zIndex: assignees.length - index }}
                >
                  <AssigneeTooltip
                    message={assignee.user.displayName}
                    inputId={crypto.randomUUID()}
                    direction="down"
                    smallSize
                    withArrow={false}
                  >
                    <CustomAvatar
                      avatarSize="x-small"
                      fontSize="x-small"
                      type="monogram"
                      initials={getInitials(assignee.user.displayName)}
                      fontBold
                    />
                  </AssigneeTooltip>
                </span>
              ))}
              {assignees.length > 3 && (
                <AssigneeTooltip
                  message={assignees
                    .slice(3)
                    .map(a => a.user.displayName)
                    .join("\n")}
                  inputId={crypto.randomUUID()}
                  direction="down"
                  smallSize
                  withArrow={false}
                >
                  <span
                    className={`${styles.assignee} ${styles.overlapped} ${styles.assigneeMore}`}
                    style={{ zIndex: 0 }}
                  >
                    +{assignees.length - 3}
                  </span>
                </AssigneeTooltip>
              )}
            </div>
          )}
          {assignees.length === 0 && (
            <span className={`${styles.unassignedIcon}`}>
              <AssigneeTooltip
                message={tActionItems("unassigned")}
                inputId={crypto.randomUUID()}
                direction="down"
                smallSize
                withArrow={false}
              >
                <Icon iconName="person-filled" altText="" />
              </AssigneeTooltip>
            </span>
          )}
        </div>
        {isPractitioner && (
          <div className={styles.status}>
            <StatusBadge
              badgeType={StatusBadgeType.ACTION_ITEM}
              type={status as ActionItemStatusType}
            />
          </div>
        )}
        <div className={styles.dueDate}>
          {dueLabel ? dueLabel : tActionItems("no-due-date")}
        </div>
        <div className={styles.icon}>
          <Icon iconName={"chevron-right"} altText="Arrow Right" size={20} />
        </div>
      </div>
    </button>
  );
};
