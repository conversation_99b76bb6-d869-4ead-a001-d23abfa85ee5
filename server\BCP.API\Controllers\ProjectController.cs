using System.ComponentModel.DataAnnotations;
using BCP.Core.BGP.ControlAPI.Spec;
using BCP.Core.Common;
using BCP.Core.Project;
using BCP.Core.User;
using BCP.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace BCP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProjectController : ControllerBase
    {
        private readonly IControlAPIService _bgpService;
        private readonly IProjectService _projectService;
        private readonly int _memberFirmId;
        private readonly IUserContext _contextService;
        private readonly DataContext _dataContext;
        private readonly IUserService _userService;
        private readonly IHostEnvironment _env;

        public ProjectController(
            IControlAPIService bgpService,
            IProjectService projectService,
            IUserContext contextService,
            WebSettings webSettings,
            IUserService userService,
            DataContext dataContext,
            IHostEnvironment env
        )
        {
            _bgpService = bgpService;
            _projectService = projectService;
            _memberFirmId = webSettings.MemberFirmId;
            _contextService = contextService;
            _dataContext = dataContext;
            _userService = userService;
            _env = env;
        }

        [HttpGet]
        public async Task<IEnumerable<Data.Models.Project>> Get(Guid clientId)
        {
            return await _projectService.GetUserProjects(clientId);
        }

        [HttpPut("editProject")]
        public async Task<ActionResult<bool>> EditProject(EditProjectRequest editProjectRequest)
        {
            if (_contextService.CurrentUser == null)
            {
                return Unauthorized();
            }
            if (!(await _userService.IsAuthorizedToAccessProject(editProjectRequest.Id, true)))
            {
                return Unauthorized();
            }
            return Ok(await _projectService.EditProject(editProjectRequest));
        }

        [HttpGet("client-projects")]
        public async Task<IActionResult> GetClientProjects(
            [Required] int memberFirmId,
            [Required] int clientId
        )
        {
            var clientProjects = await _bgpService.GetClientProjectsAsync(memberFirmId, clientId);
            return Ok(clientProjects);
        }

        [HttpGet("members")]
        public async Task<IActionResult> GetProjectMembers(
            [Required] int memberFirmId,
            [Required] int clientId,
            [Required] int projectId
        )
        {
            var projectMembers = await _bgpService.GetProjectMembersAsync(
                memberFirmId,
                clientId,
                projectId
            );
            return Ok(projectMembers);
        }

        [HttpGet("get-user-access")]
        public async Task<IActionResult> GetUserAccessForProjects(
            [FromQuery] GetUserAccessForProjectsRequest request
        )
        {
            try
            {
                var accessDetails = await _projectService.GetUserAccessForProjectsAsync(request);

                if (accessDetails.Any())
                {
                    return Ok(accessDetails);
                }

                return NotFound(
                    new { Message = "No access details found for the specified projects." }
                );
            }
            catch (Exception ex)
            {
                return StatusCode(
                    500,
                    new
                    {
                        Message = "An error occurred while fetching user access details.",
                        Details = ex.Message,
                    }
                );
            }
        }

        [HttpPost("edit-user")]
        public async Task<IActionResult> EditUser([FromBody] EditUserRequest request)
        {
            try
            {
                var result = await _projectService.AddOrRemoveUserAsync(request);

                if (result)
                {
                    return Ok(
                        new
                        {
                            Message = "User project access updated successfully.",
                            success = true,
                        }
                    );
                }

                return BadRequest(
                    new { Message = "Failed to edit user project access.", success = false }
                );
            }
            catch (ValidationException ex)
            {
                return UnprocessableEntity(
                    new
                    {
                        Message = "Validation error.",
                        Details = ex.Message,
                        success = false,
                    }
                );
            }
            catch (HttpRequestException ex)
            {
                return StatusCode(
                    502,
                    new
                    {
                        Message = "Failed to communicate with the external service.",
                        Details = ex.Message,
                        success = false,
                    }
                );
            }
            catch (Exception ex)
            {
                return StatusCode(
                    500,
                    new
                    {
                        Message = "An unexpected error occurred.",
                        Details = ex.Message,
                        success = false,
                    }
                );
            }
        }

        [HttpPost("rename")]
        public async Task<IActionResult> RenameProject(
            [FromQuery] string name,
            [FromQuery] int projectId,
            [FromQuery] int clientId
        )
        {
            try
            {
                await _contextService.CheckAccess(BcpRole.BdoClientAdmin, clientId, projectId);

                var client = await _dataContext
                    .Clients.Where(p => p.BGPId == clientId)
                    .FirstOrDefaultAsync();

                if (client == null)
                {
                    return NotFound();
                }

                var validationResult = await _bgpService.ValidateClientProjectName(name, "Project");
                if (!validationResult.IsValid)
                {
                    return StatusCode(
                        400,
                        new
                        {
                            message = $"Failed to rename project. {validationResult.ValidationMessage}",
                        }
                    );
                }

                var projectWithNameAlreadyExists = await _bgpService.CheckProjectSiteExists(
                    _memberFirmId,
                    clientId,
                    name,
                    false,
                    false,
                    false
                );

                // If a string (with project status) is returned, a project with this name already exists, throw error, else empty string, project does not exist and proceed
                if (!string.IsNullOrEmpty(projectWithNameAlreadyExists))
                {
                    return StatusCode(
                        400,
                        new { message = $"Project site with ${name} already exists." }
                    );
                }

                var renameResult = await _bgpService.RenameProject(
                    _memberFirmId,
                    clientId,
                    projectId,
                    name
                );

                if (!renameResult)
                {
                    return StatusCode(400, new { message = "Failed to rename project." });
                }
                var project = await _dataContext.Projects.FirstOrDefaultAsync(x =>
                    x.BGPId == projectId && x.ClientId == client.Id
                );

                if (project != null)
                {
                    project.Name = name;
                    await _dataContext.SaveChangesAsync();
                }
                return Ok(new { Message = "Project successfully renamed.", success = true });
            }
            catch (Exception ex)
            {
                return StatusCode(400, new { message = $"Failed to rename project. {ex.Message}" });
            }
        }

        [HttpGet("getProjectWithStages")]
        public async Task<ActionResult> GetProjectWithStages(
            [FromQuery] Guid clientId,
            [FromQuery] int projectId
        )
        {
            if (await _userService.IsAuthorizedToAccessProject(clientId, projectId, false))
            {
                return Unauthorized();
            }
            IEnumerable<Data.Models.Project> project = await _projectService.GetProjectWithStages(
                clientId,
                projectId
            );

            return Ok(project);
        }

        [HttpGet("getProjectStageById")]
        public async Task<IActionResult> GetProjectStageById([FromQuery] Guid projectStageId)
        {
            return Ok(await _projectService.GetProjectStageById(projectStageId));
        }

        [HttpPut("updateProjectStages")]
        public async Task<IActionResult> UpdateProjectStage([FromBody] Data.Models.Project project)
        {
            try
            {
                if (!(await _userService.IsAuthorizedToAccessProject(project.Id, true)))
                {
                    return Unauthorized();
                }
                await _projectService.UpdateProjectStages(project);
                return Ok(new { Message = "Successfully updated project stage", success = true });
            }
            catch (Exception e)
            {
                return StatusCode(
                    500,
                    new
                    {
                        message = e.Message,
                        inner = e.InnerException?.Message,
                        stackTrace = _env.IsDevelopment() ? e.StackTrace : null,
                    }
                );
            }
        }

        [HttpGet("completeProjectStage")]
        public async Task<IActionResult> CompleteProjectStage(
            [FromQuery] Guid projectId,
            [FromQuery] Guid projectStageId
        )
        {
            if (!(await _userService.IsAuthorizedToAccessProject(projectId, true)))
            {
                return Unauthorized();
            }
            await _projectService.CompleteProjectStage(projectId, projectStageId);
            return Ok(new { Message = "Successfully completed project stage", success = true });
        }

        [HttpGet("projectContacts")]
        public async Task<IActionResult> GetProjectContacts([FromQuery] Guid projectId)
        {
            await _contextService.CheckAccess(BcpRole.ClientUser, projectId: projectId);
            var contacts = await _projectService.GetProjectContacts(projectId);
            return Ok(contacts);
        }

        [HttpGet("apt-linking-status")]
        public async Task<IActionResult> GetAptLinkingStatus(
            [FromQuery] int clientId,
            [FromQuery] int projectId
        )
        {
            try
            {
                await _contextService.CheckAccess(BcpRole.ClientUser, clientId, projectId);

                var bgpProject = await _projectService.GetBgpProject(clientId, projectId);

                if (bgpProject == null)
                {
                    return NotFound(new { message = "Project not found" });
                }

                var isLinkedToApt = !string.IsNullOrEmpty(bgpProject.AptEngagementId);

                return Ok(
                    new
                    {
                        isLinkedToApt = isLinkedToApt,
                        aptEngagementId = bgpProject.AptEngagementId,
                    }
                );
            }
            catch (Exception ex)
            {
                return StatusCode(
                    500,
                    new { message = $"Error checking APT linking status: {ex.Message}" }
                );
            }
        }
    }
}
